package com.demo.controller;

import cn.dev33.satoken.util.SaResult;
import com.demo.entity.IllegalRecords;
import com.demo.mapper.IllegalRecordsMapper;
import com.demo.service.VideoRecordingService;
import com.demo.service.VideoProcessingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/video")
@Slf4j
public class VideoRecordingController {

    @Autowired
    private VideoRecordingService videoRecordingService;

    @Autowired
    private VideoProcessingService videoProcessingService;

    @Autowired
    private IllegalRecordsMapper illegalRecordsMapper;

    /**
     * 获取对应摄像机节点树
     */
    @GetMapping("/camera-tree")
    public SaResult getCameraTree() {
        return videoRecordingService.getCameraTree();
    }

    /**
     * 获取录制文件列表（使用数据库查询）
     */
    @GetMapping("/recordings")
    public SaResult getRecordings(
            @RequestParam String streamName,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")  LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")  LocalDateTime endTime) {
        return videoRecordingService.getRecordingsList(streamName, startTime, endTime);
    }

    /**
     * 直接播放MP4文件（支持渐进式下载）
     */
    @GetMapping("/play-direct")
    public void playDirectMp4File(
            @RequestParam String filePath,
            HttpServletRequest request,
            HttpServletResponse response) {
        try {
            File mp4File = new File(filePath);

            if (!mp4File.exists()) {
                log.warn("MP4文件不存在: {}", filePath);
                response.setStatus(404);
                response.getWriter().write("MP4 file not found: " + filePath);
                return;
            }

            long fileLength = mp4File.length();
            long start = 0;
            long end = fileLength - 1;

            // 处理Range请求
            String range = request.getHeader("Range");
            if (range != null && range.startsWith("bytes=")) {
                String[] ranges = range.substring(6).split("-");
                try {
                    start = Long.parseLong(ranges[0]);
                    if (ranges.length > 1 && !ranges[1].isEmpty()) {
                        end = Long.parseLong(ranges[1]);
                    }
                } catch (NumberFormatException e) {
                    // 忽略无效的Range请求
                }
            }

            long contentLength = end - start + 1;

            // 设置优化的响应头
            response.setContentType("video/mp4");
            response.setHeader("Accept-Ranges", "bytes");
            response.setHeader("Content-Length", String.valueOf(contentLength));
            response.setHeader("Cache-Control", "public, max-age=3600");
            response.setHeader("Access-Control-Allow-Origin", "*");

            // 添加这些头部来优化流媒体播放
            response.setHeader("Access-Control-Allow-Headers", "Range");
            response.setHeader("Access-Control-Expose-Headers", "Accept-Ranges, Content-Encoding, Content-Length, Content-Range");

            if (range != null) {
                response.setStatus(206);
                response.setHeader("Content-Range", String.format("bytes %d-%d/%d", start, end, fileLength));
            } else {
                response.setStatus(200);
            }

            // 优化的流式传输
            try (RandomAccessFile randomAccessFile = new RandomAccessFile(mp4File, "r");
                 BufferedOutputStream bos = new BufferedOutputStream(response.getOutputStream(), 8192)) {

                randomAccessFile.seek(start);
                byte[] buffer = new byte[8192]; // 减小缓冲区，提高响应速度
                long bytesToRead = contentLength;

                while (bytesToRead > 0) {
                    int bytesRead = randomAccessFile.read(buffer, 0, (int) Math.min(buffer.length, bytesToRead));
                    if (bytesRead == -1) break;

                    try {
                        bos.write(buffer, 0, bytesRead);
                        bytesToRead -= bytesRead;

                        // 每写入一定数据后刷新一次，而不是每次都刷新
                        if (bytesToRead % (8192 * 4) == 0) {
                            bos.flush();
                        }
                    } catch (IOException e) {
                        if (e.getMessage() != null && e.getMessage().contains("Connection reset by peer")) {
                            log.info("客户端连接中断，停止传输MP4文件: {}", filePath);
                            return;
                        }
                        throw e;
                    }
                }
                bos.flush(); // 最后刷新
            }

        } catch (Exception e) {
            log.error("MP4文件播放失败: {}", e.getMessage(), e);
            try {
                if (!response.isCommitted()) {
                    response.setStatus(500);
                    response.getWriter().write("MP4 playback error: " + e.getMessage());
                }
            } catch (IOException ignored) {}
        }
    }

    /**
     * 下载MP4录制文件
     * 支持通过文件路径或录制记录ID下载
     */
    @GetMapping("/download")
    public void downloadMp4File(
            @RequestParam(required = false) String filePath,
            @RequestParam(required = false) Long recordingId,
            HttpServletRequest request,
            HttpServletResponse response) {
        try {
            File mp4File = null;
            String fileName = null;

            // 优先使用recordingId，从数据库获取文件信息
            if (recordingId != null) {
                var recording = videoRecordingService.getById(recordingId);
                if (recording == null) {
                    log.warn("录制记录不存在: ID={}", recordingId);
                    response.setStatus(404);
                    response.getWriter().write("Recording not found: " + recordingId);
                    return;
                }

                filePath = recording.getFilePath();
                fileName = recording.getFileName();

                // 如果数据库中没有文件名，从路径中提取
                if (fileName == null || fileName.isEmpty()) {
                    fileName = new File(filePath).getName();
                }

                // 生成更友好的文件名：流名称_开始时间.mp4
                if (recording.getStreamName() != null && recording.getStartTime() != null) {
                    String timeStr = recording.getStartTime().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                    fileName = recording.getStreamName() + "_" + timeStr + ".mp4";
                }
            } else if (filePath != null && !filePath.isEmpty()) {
                // 使用文件路径
                fileName = new File(filePath).getName();
            } else {
                log.warn("下载请求缺少必要参数：filePath");
                response.setStatus(400);
                response.getWriter().write("Missing required parameter: filePath");
                return;
            }

            mp4File = new File(filePath);

            if (!mp4File.exists()) {
                log.warn("MP4文件不存在: {}", filePath);
                response.setStatus(404);
                response.getWriter().write("MP4 file not found: " + filePath);
                return;
            }

            // 检查文件是否可读
            if (!mp4File.canRead()) {
                log.warn("MP4文件无法读取: {}", filePath);
                response.setStatus(403);
                response.getWriter().write("MP4 file cannot be read: " + filePath);
                return;
            }

            long fileLength = mp4File.length();

            // 设置下载响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Length", String.valueOf(fileLength));

            // 处理文件名编码，支持中文文件名
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString())
                    .replaceAll("\\+", "%20");

            // 设置Content-Disposition头，让浏览器下载而不是播放
            response.setHeader("Content-Disposition",
                    "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + encodedFileName);

            // 设置缓存控制
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            // 跨域支持
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Headers", "Content-Type");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

            log.info("开始下载MP4文件: {} (大小: {} bytes)", fileName, fileLength);

            // 流式传输文件
            try (FileInputStream fis = new FileInputStream(mp4File);
                 BufferedInputStream bis = new BufferedInputStream(fis, 16384);
                 BufferedOutputStream bos = new BufferedOutputStream(response.getOutputStream(), 16384)) {

                byte[] buffer = new byte[16384]; // 16KB缓冲区
                int bytesRead;
                long totalBytesRead = 0;

                while ((bytesRead = bis.read(buffer)) != -1) {
                    try {
                        bos.write(buffer, 0, bytesRead);
                        totalBytesRead += bytesRead;

                        // 每传输1MB刷新一次
                        if (totalBytesRead % (1024 * 1024) == 0) {
                            bos.flush();
                        }
                    } catch (IOException e) {
                        if (e.getMessage() != null && e.getMessage().contains("Connection reset by peer")) {
                            log.debug("客户端连接中断，停止下载MP4文件: {}", fileName);
                            return;
                        }
                        throw e;
                    }
                }

                bos.flush();
                log.info("MP4文件下载完成: {} (传输: {} bytes)", fileName, totalBytesRead);
            }

        } catch (Exception e) {
            log.error("MP4文件下载失败: {}", e.getMessage(), e);
            try {
                if (!response.isCommitted()) {
                    response.setStatus(500);
                    response.getWriter().write("MP4 download error: " + e.getMessage());
                }
            } catch (IOException ignored) {}
        }
    }

    /**
     * 测试违法视频截取功能
     * 根据违法记录ID处理视频截取
     */
    @PostMapping("/test-illegal-video-extraction")
    public SaResult testIllegalVideoExtraction(@RequestParam String illegalRecordId) {
        try {
            log.info("开始测试违法视频截取，违法记录ID: {}", illegalRecordId);

            // 查询违法记录
            IllegalRecords illegalRecord = illegalRecordsMapper.selectById(illegalRecordId);

            if (illegalRecord == null) {
                return SaResult.error("违法记录不存在，ID: " + illegalRecordId);
            }

            if (illegalRecord.getCreateTime() == null) {
                return SaResult.error("违法记录创建时间为空");
            }

            if (illegalRecord.getEquipmentNumber() == null) {
                return SaResult.error("设备编号为空");
            }

            // 转换创建时间为LocalDateTime
            java.time.LocalDateTime createTime = illegalRecord.getCreateTime().toInstant()
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDateTime();

            log.info("违法记录信息: UUID={}, 创建时间={}, 设备编号={}",
                    illegalRecord.getUuid(), createTime, illegalRecord.getEquipmentNumber());

            // 调用视频处理服务
            SaResult result = videoProcessingService.processIllegalVideo(
                    illegalRecord.getUuid(),
                    createTime,
                    illegalRecord.getEquipmentNumber()
            );

            return result;

        } catch (Exception e) {
            log.error("测试违法视频截取时发生异常", e);
            return SaResult.error("测试失败: " + e.getMessage());
        }
    }
}
