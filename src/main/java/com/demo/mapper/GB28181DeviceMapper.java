package com.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demo.entity.GB28181Device;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * GB28181设备数据访问层
 * 提供设备信息的数据库操作方法
 */
@Mapper
public interface GB28181DeviceMapper extends BaseMapper<GB28181Device> {
    
    /**
     * 根据设备ID查找设备
     * 
     * @param deviceId 设备ID
     * @return 设备信息
     */
    @Select("SELECT * FROM gb28181_device WHERE device_id = #{deviceId}")
    GB28181Device findByDeviceId(@Param("deviceId") String deviceId);
    
    /**
     * 根据设备状态查找设备列表
     * 
     * @param status 设备状态
     * @return 设备列表
     */
    @Select("SELECT * FROM gb28181_device WHERE status = #{status}")
    List<GB28181Device> findByStatus(@Param("status") String status);
    
    /**
     * 根据服务器IP查找设备列表
     * 用于分布式部署时查询某个次服务器的设备
     * 
     * @param serverIp 服务器IP
     * @return 设备列表
     */
    @Select("SELECT * FROM gb28181_device WHERE server_ip = #{serverIp}")
    List<GB28181Device> findByServerIp(@Param("serverIp") String serverIp);
    
    /**
     * 根据地理位置查找设备
     * 
     * @param city 城市
     * @param county 县区
     * @return 设备列表
     */
    @Select("SELECT * FROM gb28181_device WHERE city = #{city} AND county = #{county}")
    List<GB28181Device> findByCityAndCounty(@Param("city") String city, @Param("county") String county);
    
    /**
     * 查找在线设备数量
     * 
     * @return 在线设备数量
     */
    @Select("SELECT COUNT(*) FROM gb28181_device WHERE status = 'online'")
    long countOnlineDevices();
    
    /**
     * 查找离线设备数量
     * 
     * @return 离线设备数量
     */
    @Select("SELECT COUNT(*) FROM gb28181_device WHERE status = 'offline'")
    long countOfflineDevices();
    
    /**
     * 查找超时未心跳的设备
     * 用于检测离线设备
     * 
     * @param timeoutTime 超时时间点
     * @return 超时设备列表
     */
    @Select("SELECT * FROM gb28181_device WHERE status = 'online' AND last_keepalive_time < #{timeoutTime}")
    List<GB28181Device> findTimeoutDevices(@Param("timeoutTime") Date timeoutTime);
    
    /**
     * 根据设备ID检查设备是否存在
     * 
     * @param deviceId 设备ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM gb28181_device WHERE device_id = #{deviceId}")
    boolean existsByDeviceId(@Param("deviceId") String deviceId);
    
    /**
     * 根据RTP端口查找设备
     * 用于端口管理
     * 
     * @param rtpPort RTP端口
     * @return 设备信息
     */
    @Select("SELECT * FROM gb28181_device WHERE rtp_port = #{rtpPort}")
    GB28181Device findByRtpPort(@Param("rtpPort") Integer rtpPort);
    
    /**
     * 查找已分配RTP端口的设备
     * 
     * @return 设备列表
     */
    @Select("SELECT * FROM gb28181_device WHERE rtp_port IS NOT NULL")
    List<GB28181Device> findDevicesWithRtpPort();
}
