package com.demo.service;

import com.demo.entity.GB28181Device;
import com.demo.mapper.GB28181DeviceMapper;
import com.demo.config.GB28181Properties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sip.message.Request;
import javax.sip.header.FromHeader;
import javax.sip.header.ContactHeader;
import javax.sip.header.ViaHeader;
import javax.sip.address.URI;
import java.util.Date;
import java.util.List;

/**
 * GB28181设备管理服务
 * 提供设备注册、验证、状态管理等功能
 */
@Slf4j
@Service
public class GB28181DeviceService {
    
    @Autowired
    private GB28181DeviceMapper deviceMapper;

    @Autowired
    private GB28181Properties properties;
    
    /**
     * 验证设备注册信息
     * 
     * @param deviceId 设备ID
     * @param password 密码
     * @return 验证结果
     */
    public boolean validateDevice(String deviceId, String password) {
        // 验证设备ID格式（20位数字）
        if (deviceId == null || !deviceId.matches("\\d{20}")) {
            log.info("设备ID格式不正确: {}", deviceId);
            return false;
        }
        
        // 验证密码（这里简单验证，实际项目中可以从数据库或配置中获取）
        if (!"ybda1234".equals(password)) {
            log.info("设备密码验证失败: {}", deviceId);
            return false;
        }
        
        return true;
    }
    
    /**
     * 保存设备注册信息
     * 
     * @param deviceId 设备ID
     * @param request SIP注册请求
     */
    @Transactional
    public void saveDevice(String deviceId, Request request) {
        try {
            GB28181Device device = deviceMapper.findByDeviceId(deviceId);
            
            if (device == null) {
                device = new GB28181Device();
                device.setDeviceId(deviceId);
                device.setCreatedTime(new Date());
            }
            
            // 设置基本信息
            device.setStatus("online");
            device.setLastRegisterTime(new Date());
            device.setLastKeepaliveTime(new Date());
            device.setUpdatedTime(new Date());
            
            // 从SIP请求中提取设备信息
            extractDeviceInfoFromRequest(device, request);
            
            if (device.getId() == null) {
                deviceMapper.insert(device);
            } else {
                deviceMapper.updateById(device);
            }
            
            log.info("设备注册信息保存成功: {}", deviceId);
        } catch (Exception e) {
            log.error("保存设备注册信息失败: {}", deviceId, e);
            throw new RuntimeException("保存设备信息失败", e);
        }
    }
    
    /**
     * 从SIP请求中提取设备信息
     *
     * @param device 设备对象
     * @param request SIP请求
     */
    private void extractDeviceInfoFromRequest(GB28181Device device, Request request) {
        try {
            // 提取设备IP和端口
            ContactHeader contactHeader = (ContactHeader) request.getHeader(ContactHeader.NAME);
            if (contactHeader != null) {
                URI contactUri = contactHeader.getAddress().getURI();
                if (contactUri.isSipURI()) {
                    javax.sip.address.SipURI sipUri = (javax.sip.address.SipURI) contactUri;
                    device.setIpAddress(sipUri.getHost());
                    device.setPort(sipUri.getPort());
                }
            }

            // 提取传输协议信息
            ViaHeader viaHeader = (ViaHeader) request.getHeader(ViaHeader.NAME);
            if (viaHeader != null) {
                String transport = viaHeader.getTransport();
                device.setTransportProtocol(transport != null ? transport.toUpperCase() : "UDP");
                log.info("设备{}注册使用协议: {}", device.getDeviceId(), device.getTransportProtocol());
            } else {
                // 默认使用UDP
                device.setTransportProtocol("UDP");
            }

            // 可以从User-Agent头提取厂商和型号信息
            // UserAgentHeader userAgentHeader = (UserAgentHeader) request.getHeader(UserAgentHeader.NAME);
            // if (userAgentHeader != null) {
            //     device.setManufacturer(extractManufacturer(userAgentHeader.toString()));
            //     device.setModel(extractModel(userAgentHeader.toString()));
            // }

        } catch (Exception e) {
            log.info("提取设备信息失败: {}", device.getDeviceId(), e);
        }
    }
    
    /**
     * 更新设备心跳时间
     * 
     * @param deviceId 设备ID
     */
    @Transactional
    public void updateKeepalive(String deviceId) {
        GB28181Device device = deviceMapper.findByDeviceId(deviceId);
        if (device != null) {
            device.setLastKeepaliveTime(new Date());
            device.setStatus("online");
            device.setUpdatedTime(new Date());
            deviceMapper.updateById(device);
            
            log.info("更新设备心跳: {}", deviceId);
        }
    }
    
    /**
     * 设置设备离线
     * 
     * @param deviceId 设备ID
     */
    @Transactional
    public void setDeviceOffline(String deviceId) {
        GB28181Device device = deviceMapper.findByDeviceId(deviceId);
        if (device != null) {
            device.setStatus("offline");
            device.setRtpPort(null); // 清除RTP端口分配
            device.setUpdatedTime(new Date());
            deviceMapper.updateById(device);
            
            log.info("设备设置为离线: {}", deviceId);
        }
    }
    
    /**
     * 分配RTP端口给设备
     * 
     * @param deviceId 设备ID
     * @param rtpPort RTP端口
     */
    @Transactional
    public void allocateRtpPort(String deviceId, Integer rtpPort) {
        GB28181Device device = deviceMapper.findByDeviceId(deviceId);
        if (device != null) {
            device.setRtpPort(rtpPort);
            device.setUpdatedTime(new Date());
            deviceMapper.updateById(device);
            
            log.info("为设备{}分配RTP端口: {}", deviceId, rtpPort);
        }
    }
    
    /**
     * 获取设备信息
     * 
     * @param deviceId 设备ID
     * @return 设备信息
     */
    public GB28181Device getDevice(String deviceId) {
        return deviceMapper.findByDeviceId(deviceId);
    }
    
    /**
     * 获取所有在线设备
     * 
     * @return 在线设备列表
     */
    public List<GB28181Device> getOnlineDevices() {
        return deviceMapper.findByStatus("online");
    }
    
    /**
     * 获取所有设备
     * 
     * @return 所有设备列表
     */
    public List<GB28181Device> getAllDevices() {
        return deviceMapper.selectList(null);
    }
    
    /**
     * 检查设备超时并设置离线
     * 定时任务调用，检查长时间未心跳的设备
     * 
     * @param timeoutMinutes 超时分钟数
     */
    @Transactional
    public void checkDeviceTimeout(int timeoutMinutes) {
        Date timeoutTime = new Date(System.currentTimeMillis() - timeoutMinutes * 60 * 1000L);
        List<GB28181Device> timeoutDevices = deviceMapper.findTimeoutDevices(timeoutTime);
        
        for (GB28181Device device : timeoutDevices) {
            setDeviceOffline(device.getDeviceId());
        }
        
        if (!timeoutDevices.isEmpty()) {
            log.info("检查到{}个超时设备，已设置为离线", timeoutDevices.size());
        }
    }

    /**
     * 获取或分配RTP端口（复用已有端口）
     *
     * @param deviceId 设备ID
     * @return RTP端口号
     */
    public int getOrAllocateRtpPort(String deviceId) {
        GB28181Device device = getDevice(deviceId);

        // 如果设备已有分配的端口，复用该端口
        if (device != null && device.getRtpPort() != null) {
            log.info("复用设备{}已分配的RTP端口: {}", deviceId, device.getRtpPort());
            return device.getRtpPort();
        }

        // 分配新端口
        int newPort = allocateNewRtpPort();

        // 保存端口到数据库
        if (device != null) {
            device.setRtpPort(newPort);
            device.setUpdatedTime(new Date());
            deviceMapper.updateById(device);
            log.info("为设备{}分配新RTP端口并保存: {}", deviceId, newPort);
        }

        return newPort;
    }

    /**
     * 分配新的RTP端口
     *
     * @return 新分配的端口号
     */
    private int allocateNewRtpPort() {
        int startPort = properties.getZlm().getRtpPortStart();
        int endPort = properties.getZlm().getRtpPortEnd();

        for (int port = startPort; port <= endPort; port++) {
            // 检查端口是否已被其他设备使用
            if (!isPortInUse(port)) {
                return port;
            }
        }

        throw new RuntimeException("无可用的RTP端口");
    }

    /**
     * 检查端口是否被使用
     *
     * @param port 端口号
     * @return 是否被使用
     */
    private boolean isPortInUse(int port) {
        // 查询数据库中是否有设备使用该端口
        return deviceMapper.selectList(null).stream()
                .anyMatch(device -> device.getRtpPort() != null && device.getRtpPort().equals(port));
    }

    /**
     * 更新设备信息
     *
     * @param device 设备信息
     */
    @Transactional
    public void updateDevice(GB28181Device device) {
        device.setUpdatedTime(new Date());
        deviceMapper.updateById(device);
    }
}
