package com.demo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 推流控制配置
 * 用于管理推流权限和时间控制
 */
@Data
@Component
@ConfigurationProperties(prefix = "stream")
public class StreamConfig {
    
    /**
     * 推流总开关
     * true: 允许所有设备推流（不考虑观看者）
     * false: 按观看者逻辑控制推流权限
     */
    private Boolean tuiliu = false;
    
    /**
     * 允许推流的时间段配置
     */
    private AllowedTime allowedTime = new AllowedTime();
    
    @Data
    public static class AllowedTime {
        /**
         * 允许推流开始时间（支持格式：整数小时如9，或时间字符串如"09:00:00"）
         */
        private String start = "9";

        /**
         * 允许推流结束时间（支持格式：整数小时如23，或时间字符串如"23:00:00"）
         */
        private String end = "23";
    }
    
    /**
     * 检查当前时间是否在允许推流的时间段内
     * 支持两种时间格式：
     * 1. 整数格式：如 "9" 表示9:00:00
     * 2. 时间格式：如 "18:59:59" 表示精确时间
     * @return true表示在允许时间段内，false表示不在
     */
    public boolean isInAllowedTimeRange() {
        java.time.LocalTime now = java.time.LocalTime.now();

        java.time.LocalTime startTime = parseTimeString(allowedTime.getStart());
        java.time.LocalTime endTime = parseTimeString(allowedTime.getEnd());

        // 处理跨天的情况，如22:00-06:00
        if (!startTime.isAfter(endTime)) {
            // 正常情况，如09:00-18:59
            return !now.isBefore(startTime) && !now.isAfter(endTime);
        } else {
            // 跨天情况，如22:00-06:00
            return !now.isBefore(startTime) || !now.isAfter(endTime);
        }
    }

    /**
     * 解析时间字符串，支持多种格式
     * @param timeStr 时间字符串，支持格式：
     *                - 整数：如 "9" -> 09:00:00
     *                - HH:mm：如 "18:59" -> 18:59:00
     *                - HH:mm:ss：如 "18:59:59" -> 18:59:59
     * @return LocalTime对象
     */
    private java.time.LocalTime parseTimeString(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return java.time.LocalTime.of(0, 0, 0);
        }

        timeStr = timeStr.trim();

        try {
            // 如果是纯数字，当作小时处理
            if (timeStr.matches("\\d+")) {
                int hour = Integer.parseInt(timeStr);
                return java.time.LocalTime.of(hour, 0, 0);
            }

            // 如果包含冒号，按时间格式解析
            if (timeStr.contains(":")) {
                String[] parts = timeStr.split(":");

                if (parts.length == 2) {
                    // HH:mm格式
                    int hour = Integer.parseInt(parts[0]);
                    int minute = Integer.parseInt(parts[1]);
                    return java.time.LocalTime.of(hour, minute, 0);
                } else if (parts.length == 3) {
                    // HH:mm:ss格式
                    int hour = Integer.parseInt(parts[0]);
                    int minute = Integer.parseInt(parts[1]);
                    int second = Integer.parseInt(parts[2]);
                    return java.time.LocalTime.of(hour, minute, second);
                }
            }

            // 如果以上都不匹配，尝试直接解析
            return java.time.LocalTime.parse(timeStr);

        } catch (Exception e) {
            // 解析失败时，返回默认时间并记录警告
            System.err.println("警告：时间格式解析失败，使用默认值 00:00:00。输入值：" + timeStr + "，错误：" + e.getMessage());
            return java.time.LocalTime.of(0, 0, 0);
        }
    }
}
