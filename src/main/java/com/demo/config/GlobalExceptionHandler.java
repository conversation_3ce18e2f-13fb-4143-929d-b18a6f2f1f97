package com.demo.config;

import cn.dev33.satoken.exception.SaTokenException;
import cn.dev33.satoken.util.SaResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.io.IOException;
import org.apache.catalina.connector.ClientAbortException;

/**
 * <AUTHOR>
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    @ExceptionHandler(ShiftOperationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public SaResult handleShiftOperationException(ShiftOperationException e) {
        log.error("ShiftOperationException occurred: {}", e.getMessage(), e);
        return SaResult.error(e.getMessage()).setCode(HttpStatus.BAD_REQUEST.value());
    }

    @ExceptionHandler(SaTokenException.class)
    public SaResult handlerSaTokenException(SaTokenException e) {
        // 根据不同异常细分状态码返回不同的提示
        if(e.getCode() == 11011) {
            return SaResult.error("未能读取到有效Token").setCode(403);
        }
        if(e.getCode() == 11012) {
            return SaResult.error("Token无效").setCode(403);
        }
        if(e.getCode() == 11013) {
            return SaResult.error("Token已过期").setCode(403);
        }
        if(e.getCode() == 11014) {
            return SaResult.error("Token已被顶下线").setCode(403);
        }
        if(e.getCode() == 11015) {
            return SaResult.error("Token已被踢下线").setCode(403);
        }
        if(e.getCode() == 11016) {
            return SaResult.error("Token已被冻结").setCode(403);
        }
        // 默认提示
        return SaResult.error(e.getMessage());
    }
    @ExceptionHandler(NullPointerException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public SaResult handleNullPointerException(NullPointerException e) {
        log.error("NullPointerException occurred: {}", e.getMessage(), e);
        return SaResult.error("请求参数为空或不正确").setCode(HttpStatus.BAD_REQUEST.value());
    }

    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public SaResult handleIllegalArgumentException(IllegalArgumentException e) {
        log.error("IllegalArgumentException occurred: {}", e.getMessage(), e);
        return SaResult.error("请求参数无效: " + e.getMessage()).setCode(HttpStatus.BAD_REQUEST.value());
    }

    @ExceptionHandler(IllegalStateException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public SaResult handleIllegalStateException(IllegalStateException e) {
        log.error("IllegalStateException occurred: {}", e.getMessage(), e);
        return SaResult.error("当前操作非法: " + e.getMessage()).setCode(HttpStatus.CONFLICT.value());
    }

    @ExceptionHandler(ClientAbortException.class)
    public void handleClientAbortException(ClientAbortException e) {
        // 客户端连接中断，这是正常情况，只记录debug级别日志
        log.info("客户端连接中断: {}", e.getMessage());
        // 不返回任何响应，因为连接已断开
    }

    @ExceptionHandler(IOException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public SaResult handleIOException(IOException e) {
        // 检查是否是连接重置异常
        if (e.getMessage() != null && e.getMessage().contains("Connection reset by peer")) {
            log.info("客户端连接重置: {}", e.getMessage());
            // 对于连接重置，不返回响应
            return null;
        }

        log.error("IOException occurred: {}", e.getMessage(), e);
        return SaResult.error("IO异常: " + e.getMessage()).setCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
    }

    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public SaResult handleRuntimeException(RuntimeException e) {
        log.error("RuntimeException occurred: {}", e.getMessage(), e);
        return SaResult.error("服务器内部错误: " + e.getMessage()).setCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
    }

}

