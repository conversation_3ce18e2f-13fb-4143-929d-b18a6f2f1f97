package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * GB28181设备实体类
 * 用于存储摄像机设备信息
 */
@Data
@TableName("gb28181_device")
public class GB28181Device {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 设备ID
     * 20位数字，符合GB28181规范
     */
    @TableField("device_id")
    private String deviceId;
    
    /**
     * 设备名称
     */
    @TableField("device_name")
    private String deviceName;

    /**
     * 设备厂商
     */
    @TableField("manufacturer")
    private String manufacturer;

    /**
     * 设备型号
     */
    @TableField("model")
    private String model;

    /**
     * 设备状态
     * online: 在线, offline: 离线
     */
    @TableField("status")
    private String status = "offline";

    /**
     * 设备IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 设备端口
     */
    @TableField("port")
    private Integer port;
    
    /**
     * 最后注册时间
     */
    @TableField("last_register_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastRegisterTime;

    /**
     * 最后心跳时间
     */
    @TableField("last_keepalive_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastKeepaliveTime;

    /**
     * 创建时间
     */
    @TableField("created_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedTime;

    /**
     * 所属服务器IP
     * 用于分布式部署时标识设备所在的次服务器
     */
    @TableField("server_ip")
    private String serverIp;

    /**
     * 当前分配的RTP端口
     */
    @TableField("rtp_port")
    private Integer rtpPort;

    /**
     * 设备所在城市
     */
    @TableField("city")
    private String city;

    /**
     * 设备所在县区
     */
    @TableField("county")
    private String county;

    /**
     * 设备所在乡镇
     */
    @TableField("township")
    private String township;

    /**
     * 设备注册时使用的传输协议
     * UDP 或 TCP
     */
    @TableField("transport_protocol")
    private String transportProtocol;
}
