DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=GB28181-1756387359473, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=sipdebug.log}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="GB28181-1756387359473"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@2cd2c764 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@d17d554]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.TCPMessageProcessor@7167d81b listeningPoint = gov.nist.javax.sip.ListeningPointImpl@7cadf3ca]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@d17d554]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@2d114d27]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@7cadf3ca]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@2d114d27]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 434]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK550
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387365303"
isSender="false" 
transactionId="z9hg4bk550" 
callId="00006D6B00002195@***********" 
firstLine="REGISTER sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK550
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk550]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK550]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( REGISTER:z9hG4bK550):gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for REGISTER sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@626bd3b4 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process REGISTER sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK550]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1 branchID = z9hG4bK550 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK550
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00006d6b00002195@***********:000025ee) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00006d6b00002195@***********:000025ee]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [REGISTER transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@7ba6b123]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@7ba6b123]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@230b3dd2]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing REGISTER sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@230b3dd2nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@7ba6b123]source = gov.nist.javax.sip.SipProviderImpl@7ba6b123]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : REGISTER sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk550 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk550]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk550 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk550]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk550]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00006d6b00002195@***********:000025ee) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1 branchID = z9hG4bK550 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:481][GB28181SipServer.java:236][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:481][GB28181SipServer.java:236][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 401 Unauthorized
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK550
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 1 REGISTER
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 401 Unauthorized
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK550
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 1 REGISTER
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387365332"
isSender="true" 
transactionId="z9hg4bk550" 
callId="00006D6B00002195@***********" 
firstLine="SIP/2.0 401 Unauthorized" 
>
<![CDATA[SIP/2.0 401 Unauthorized
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK550
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 1 REGISTER
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk550]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk550]
DEBUG - gov.nist.javax.sip.SipStackImpl.stop(SipStackImpl.java:931) [stopStack -- stoppping the stack]
DEBUG - gov.nist.javax.sip.stack.UDPMessageProcessor.run(UDPMessageProcessor.java:251) [UDPMessageProcessor: Stopping]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:455) [Stopped event scanner!!]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=GB28181-1756387394319, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=sipdebug.log}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="GB28181-1756387394319"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@516155b5 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@799fc4c9]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.TCPMessageProcessor@6c6b00f1 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@48126a17]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@799fc4c9]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@7a14ab66]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@48126a17]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@7a14ab66]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 434]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK56f
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387396312"
isSender="false" 
transactionId="z9hg4bk56f" 
callId="00006D6B00002195@***********" 
firstLine="REGISTER sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK56f
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( REGISTER:z9hG4bK56f):gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for REGISTER sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@8f1ccc6 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process REGISTER sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306 branchID = z9hG4bK56f isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK56f
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00006d6b00002195@***********:000025ee) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00006d6b00002195@***********:000025ee]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [REGISTER transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@16c5deba]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing REGISTER sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@16c5debanevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : REGISTER sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk56f existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk56f existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00006d6b00002195@***********:000025ee) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306 branchID = z9hG4bK56f isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:228][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:228][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK56f
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 2 REGISTER
Date: Thu, 28 Aug 2025 13:23:17 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK56f
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 2 REGISTER
Date: Thu, 28 Aug 2025 13:23:17 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387397217"
isSender="true" 
transactionId="z9hg4bk56f" 
callId="00006D6B00002195@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK56f
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 2 REGISTER
Date: Thu, 28 Aug 2025 13:23:17 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk56f]
DEBUG - gov.nist.javax.sip.SipProviderImpl.getNewClientTransaction(SipProviderImpl.java:333) [could not find existing transaction for INVITE sip:34020000001110022400@***********:15090 SIP/2.0
 creating a new one ]
DEBUG - gov.nist.javax.sip.stack.DefaultRouter.getNextHop(DefaultRouter.java:235) [Used request-URI for nextHop = ***********:15090/TCP]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(fe0caf3189eea794022728a0e72e6e54@0:0:0:0:0:0:0:0:zlm1756387400252) : returning null]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.<init>(TCPMessageChannel.java:167) [creating new TCPMessageChannel ]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[TCPMessageChannel.java:168][TCPMessageProcessor.java:225][SIPTransactionStack.java:1224][SipProviderImpl.java:379][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.TCPMessageProcessor.createMessageChannel(TCPMessageProcessor.java:233) [key tcp:***********:15090]
DEBUG - gov.nist.javax.sip.stack.TCPMessageProcessor.createMessageChannel(TCPMessageProcessor.java:235) [Creating gov.nist.javax.sip.stack.TCPMessageChannel@499556fe]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.<init>(SIPTransaction.java:352) [use count for encapsulated channelgov.nist.javax.sip.stack.SIPClientTransaction@ffffffff 1]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.<init>(SIPClientTransaction.java:301) [Creating clientTransaction gov.nist.javax.sip.stack.SIPClientTransaction@ffffffff]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPClientTransaction.java:302][SIPTransactionStack.java:1253][SIPTransactionStack.java:1231][SipProviderImpl.java:379][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKd14fd81dfda289c0b5199b3eaadf08b0]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1417) [ putTransactionHash :  key = z9hg4bkd14fd81dfda289c0b5199b3eaadf08b0]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKd14fd81dfda289c0b5199b3eaadf08b0]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setRemoteParty(SIPDialog.java:443) [settingRemoteParty <sip:34020000001110022400@***********:15090>]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLocalSequenceNumber(SIPDialog.java:1247) [setLocalSequenceNumber: original 	0 new  = 1]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1167) [Transaction Added gov.nist.javax.sip.stack.SIPDialog@37e682a2zlm1756387400252/null]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1169) [TID = z9hg4bkd14fd81dfda289c0b5199b3eaadf08b0/false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:1172][SIPDialog.java:327][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:329) [Creating a dialog : gov.nist.javax.sip.stack.SIPDialog@37e682a2]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:330) [provider port = 15060]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:332][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setDialog(SIPClientTransaction.java:1521) [setDialog: fe0caf3189eea794022728a0e72e6e54@0:0:0:0:0:0:0:0:zlm1756387400252sipDialog = gov.nist.javax.sip.stack.SIPDialog@37e682a2]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendRequest(SIPClientTransaction.java:918) [sendRequest() INVITE sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: fe0caf3189eea794022728a0e72e6e54@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387400252
To: <sip:34020000001110022400@***********:15090>
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bKd14fd81dfda289c0b5199b3eaadf08b0
Max-Forwards: 70
Contact: <sip:**********2000000001@***********:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:**********,**********2000000001:1
Content-Type: application/sdp
Content-Length: 325

v=0
o=**********2000000001 0 0 IN IP4 ***************
s=Play
u=**********2000000001:0
c=IN IP4 ***************
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=**********
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:424) [Sending Message INVITE sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: fe0caf3189eea794022728a0e72e6e54@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387400252
To: <sip:34020000001110022400@***********:15090>
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bKd14fd81dfda289c0b5199b3eaadf08b0
Max-Forwards: 70
Contact: <sip:**********2000000001@***********:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:**********,**********2000000001:1
Content-Type: application/sdp
Content-Length: 325

v=0
o=**********2000000001 0 0 IN IP4 ***************
s=Play
u=**********2000000001:0
c=IN IP4 ***************
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=**********
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:426) [TransactionState null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKd14fd81dfda289c0b5199b3eaadf08b0]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Calling Transaction gov.nist.javax.sip.stack.SIPClientTransaction@2fa87e50 branchID = z9hG4bKd14fd81dfda289c0b5199b3eaadf08b0 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:465][SIPClientTransaction.java:983][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPClientTransaction@2fa87e50 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:139) [sendBytes TCP inAddr *********** port = 15090 length = 905]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:163) [inaddr = /***********]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:165) [port = 15090]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@2fa87e50 branchID = z9hG4bKd14fd81dfda289c0b5199b3eaadf08b0 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:489][SIPClientTransaction.java:983][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@2fa87e50 branchID = z9hG4bKd14fd81dfda289c0b5199b3eaadf08b0 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:986][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:201) [removing  = gov.nist.javax.sip.stack.SIPClientTransaction@2fa87e50 isReliable true]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bkd14fd81dfda289c0b5199b3eaadf08b0 transaction = gov.nist.javax.sip.stack.SIPClientTransaction@2fa87e50]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1352) [REMOVED client tx gov.nist.javax.sip.stack.SIPClientTransaction@2fa87e50 KEY = z9hg4bkd14fd81dfda289c0b5199b3eaadf08b0]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@776efecd]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@776efecd]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1364][SIPClientTransaction.java:206][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@4b7b1bf0]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:258) [Client Use Count = 1]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@4b7b1bf0nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@776efecd]source = gov.nist.javax.sip.SipProviderImpl@776efecd]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = gov.nist.javax.sip.stack.SIPClientTransaction@2fa87e50]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = null]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.fireTimeoutTimer(SIPServerTransaction.java:1202) [SIPServerTransaction.fireTimeoutTimer this = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306 current state = Completed Transaction method = REGISTER]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306 branchID = z9hG4bK56f isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:1227][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk56f transaction = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@45bcfcef]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@45bcfcefnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk56f transaction = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 563]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>1</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387456308"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( MESSAGE:z9hG4bK00004823):gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for MESSAGE sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@64eaf8c0 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>1</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00004ae100006784@***********:000018be]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [MESSAGE transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@60d57085]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@60d57085nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : MESSAGE sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Date: Thu, 28 Aug 2025 13:24:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Date: Thu, 28 Aug 2025 13:24:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387456614"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Date: Thu, 28 Aug 2025 13:24:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.fireTimeoutTimer(SIPServerTransaction.java:1202) [SIPServerTransaction.fireTimeoutTimer this = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 current state = Completed Transaction method = MESSAGE]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:1227][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@481f6d4]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@481f6d4nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 563]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>2</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387516303"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( MESSAGE:z9hG4bK00004823):gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for MESSAGE sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@2e195c33 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>2</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00004ae100006784@***********:000018be]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [MESSAGE transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@20deb780]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@20deb780nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : MESSAGE sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Date: Thu, 28 Aug 2025 13:25:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Date: Thu, 28 Aug 2025 13:25:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387516602"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Date: Thu, 28 Aug 2025 13:25:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.fireTimeoutTimer(SIPServerTransaction.java:1202) [SIPServerTransaction.fireTimeoutTimer this = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 current state = Completed Transaction method = MESSAGE]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:1227][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@1736c92f]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@1736c92fnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 563]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 3 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>3</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387576309"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 3 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( MESSAGE:z9hG4bK00004823):gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for MESSAGE sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@5e594ee2 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 3 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>3</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00004ae100006784@***********:000018be]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [MESSAGE transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@509eab59]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@509eab59nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : MESSAGE sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 3 MESSAGE
Date: Thu, 28 Aug 2025 13:26:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 3 MESSAGE
Date: Thu, 28 Aug 2025 13:26:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387576851"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 3 MESSAGE
Date: Thu, 28 Aug 2025 13:26:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.fireTimeoutTimer(SIPServerTransaction.java:1202) [SIPServerTransaction.fireTimeoutTimer this = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 current state = Completed Transaction method = MESSAGE]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:1227][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@35cf14a2]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@35cf14a2nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 563]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 4 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>4</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387636315"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 4 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( MESSAGE:z9hG4bK00004823):gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for MESSAGE sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@2e11bc61 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 4 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>4</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00004ae100006784@***********:000018be]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [MESSAGE transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@20d657d3]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@20d657d3nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : MESSAGE sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 4 MESSAGE
Date: Thu, 28 Aug 2025 13:27:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 4 MESSAGE
Date: Thu, 28 Aug 2025 13:27:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387636702"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 4 MESSAGE
Date: Thu, 28 Aug 2025 13:27:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.fireTimeoutTimer(SIPServerTransaction.java:1202) [SIPServerTransaction.fireTimeoutTimer this = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 current state = Completed Transaction method = MESSAGE]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:1227][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@6f9275eb]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@6f9275ebnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 563]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 5 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>5</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387696317"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 5 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( MESSAGE:z9hG4bK00004823):gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for MESSAGE sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@75a9a00d messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 5 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>5</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00004ae100006784@***********:000018be]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [MESSAGE transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@7b6e50c8]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@7b6e50c8nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : MESSAGE sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 5 MESSAGE
Date: Thu, 28 Aug 2025 13:28:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 5 MESSAGE
Date: Thu, 28 Aug 2025 13:28:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387696908"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 5 MESSAGE
Date: Thu, 28 Aug 2025 13:28:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.fireTimeoutTimer(SIPServerTransaction.java:1202) [SIPServerTransaction.fireTimeoutTimer this = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 current state = Completed Transaction method = MESSAGE]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:1227][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@64ae099b]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@64ae099bnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 563]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 6 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>6</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387756315"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 6 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( MESSAGE:z9hG4bK00004823):gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for MESSAGE sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@cda3187 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 6 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>6</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00004ae100006784@***********:000018be]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [MESSAGE transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@21dde6c]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@21dde6cnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : MESSAGE sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 6 MESSAGE
Date: Thu, 28 Aug 2025 13:29:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 6 MESSAGE
Date: Thu, 28 Aug 2025 13:29:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387756886"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 6 MESSAGE
Date: Thu, 28 Aug 2025 13:29:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 431]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK6df
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 3 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387764356"
isSender="false" 
transactionId="z9hg4bk6df" 
callId="00006D6B00002195@***********" 
firstLine="REGISTER sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK6df
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 3 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk6df]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK6df]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( REGISTER:z9hG4bK6df):gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for REGISTER sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@3ac889e6 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process REGISTER sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK6df]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59 branchID = z9hG4bK6df isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK6df
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 3 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00006d6b00002195@***********:000025ee) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00006d6b00002195@***********:000025ee]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [REGISTER transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@340f60cf]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing REGISTER sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@340f60cfnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : REGISTER sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk6df existing={z9hg4bk00004823=gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk6df]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk6df existing={z9hg4bk00004823=gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk6df]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk6df]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00006d6b00002195@***********:000025ee) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59 branchID = z9hG4bK6df isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:228][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:228][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK6df
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 3 REGISTER
Date: Thu, 28 Aug 2025 13:29:24 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK6df
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 3 REGISTER
Date: Thu, 28 Aug 2025 13:29:24 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387764646"
isSender="true" 
transactionId="z9hg4bk6df" 
callId="00006D6B00002195@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK6df
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 3 REGISTER
Date: Thu, 28 Aug 2025 13:29:24 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk6df]
DEBUG - gov.nist.javax.sip.SipProviderImpl.getNewClientTransaction(SipProviderImpl.java:333) [could not find existing transaction for INVITE sip:34020000001110022400@***********:15090 SIP/2.0
 creating a new one ]
DEBUG - gov.nist.javax.sip.stack.DefaultRouter.getNextHop(DefaultRouter.java:235) [Used request-URI for nextHop = ***********:15090/TCP]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(bb3b3ad9edb4595666d7d5baf3ec7e19@0:0:0:0:0:0:0:0:zlm1756387766950) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.<init>(SIPTransaction.java:352) [use count for encapsulated channelgov.nist.javax.sip.stack.SIPClientTransaction@ffffffff 2]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.<init>(SIPClientTransaction.java:301) [Creating clientTransaction gov.nist.javax.sip.stack.SIPClientTransaction@ffffffff]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPClientTransaction.java:302][SIPTransactionStack.java:1253][SIPTransactionStack.java:1231][SipProviderImpl.java:379][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKa8a6457647f47b5d817a8bda271d3ae1]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1417) [ putTransactionHash :  key = z9hg4bka8a6457647f47b5d817a8bda271d3ae1]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKa8a6457647f47b5d817a8bda271d3ae1]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setRemoteParty(SIPDialog.java:443) [settingRemoteParty <sip:34020000001110022400@***********:15090>]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLocalSequenceNumber(SIPDialog.java:1247) [setLocalSequenceNumber: original 	0 new  = 1]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1167) [Transaction Added gov.nist.javax.sip.stack.SIPDialog@3d0ba5eezlm1756387766950/null]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1169) [TID = z9hg4bka8a6457647f47b5d817a8bda271d3ae1/false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:1172][SIPDialog.java:327][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:329) [Creating a dialog : gov.nist.javax.sip.stack.SIPDialog@3d0ba5ee]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:330) [provider port = 15060]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:332][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setDialog(SIPClientTransaction.java:1521) [setDialog: bb3b3ad9edb4595666d7d5baf3ec7e19@0:0:0:0:0:0:0:0:zlm1756387766950sipDialog = gov.nist.javax.sip.stack.SIPDialog@3d0ba5ee]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendRequest(SIPClientTransaction.java:918) [sendRequest() INVITE sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: bb3b3ad9edb4595666d7d5baf3ec7e19@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387766950
To: <sip:34020000001110022400@***********:15090>
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bKa8a6457647f47b5d817a8bda271d3ae1
Max-Forwards: 70
Contact: <sip:**********2000000001@***********:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:**********,**********2000000001:1
Content-Type: application/sdp
Content-Length: 325

v=0
o=**********2000000001 0 0 IN IP4 ***************
s=Play
u=**********2000000001:0
c=IN IP4 ***************
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=**********
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:424) [Sending Message INVITE sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: bb3b3ad9edb4595666d7d5baf3ec7e19@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387766950
To: <sip:34020000001110022400@***********:15090>
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bKa8a6457647f47b5d817a8bda271d3ae1
Max-Forwards: 70
Contact: <sip:**********2000000001@***********:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:**********,**********2000000001:1
Content-Type: application/sdp
Content-Length: 325

v=0
o=**********2000000001 0 0 IN IP4 ***************
s=Play
u=**********2000000001:0
c=IN IP4 ***************
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=**********
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:426) [TransactionState null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKa8a6457647f47b5d817a8bda271d3ae1]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Calling Transaction gov.nist.javax.sip.stack.SIPClientTransaction@831b2f2 branchID = z9hG4bKa8a6457647f47b5d817a8bda271d3ae1 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:465][SIPClientTransaction.java:983][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPClientTransaction@831b2f2 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:139) [sendBytes TCP inAddr *********** port = 15090 length = 905]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:163) [inaddr = /***********]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:165) [port = 15090]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@831b2f2 branchID = z9hG4bKa8a6457647f47b5d817a8bda271d3ae1 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:489][SIPClientTransaction.java:983][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@831b2f2 branchID = z9hG4bKa8a6457647f47b5d817a8bda271d3ae1 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:986][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk6df]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:201) [removing  = gov.nist.javax.sip.stack.SIPClientTransaction@831b2f2 isReliable true]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bka8a6457647f47b5d817a8bda271d3ae1 transaction = gov.nist.javax.sip.stack.SIPClientTransaction@831b2f2]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1352) [REMOVED client tx gov.nist.javax.sip.stack.SIPClientTransaction@831b2f2 KEY = z9hg4bka8a6457647f47b5d817a8bda271d3ae1]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@776efecd]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@776efecd]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1364][SIPClientTransaction.java:206][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@4570db90]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:258) [Client Use Count = 2]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@4570db90nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@776efecd]source = gov.nist.javax.sip.SipProviderImpl@776efecd]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = gov.nist.javax.sip.stack.SIPClientTransaction@831b2f2]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = null]
DEBUG - gov.nist.javax.sip.SipStackImpl.stop(SipStackImpl.java:931) [stopStack -- stoppping the stack]
DEBUG - gov.nist.javax.sip.stack.UDPMessageProcessor.run(UDPMessageProcessor.java:251) [UDPMessageProcessor: Stopping]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.close(TCPMessageChannel.java:200) [Closing message Channel gov.nist.javax.sip.stack.TCPMessageChannel@499556fe]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:455) [Stopped event scanner!!]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=GB28181-1756387795009, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=sipdebug.log}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="GB28181-1756387795009"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@5e83298e listeningPoint = gov.nist.javax.sip.ListeningPointImpl@7167d81b]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.TCPMessageProcessor@7dbd70fd listeningPoint = gov.nist.javax.sip.ListeningPointImpl@2cb53bfc]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@7167d81b]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@799fc4c9]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@2cb53bfc]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@799fc4c9]
DEBUG - gov.nist.javax.sip.SipStackImpl.stop(SipStackImpl.java:931) [stopStack -- stoppping the stack]
DEBUG - gov.nist.javax.sip.stack.UDPMessageProcessor.run(UDPMessageProcessor.java:251) [UDPMessageProcessor: Stopping]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:455) [Stopped event scanner!!]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=GB28181-1756387804847, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=sipdebug.log}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="GB28181-1756387804847"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@265c0752 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@7d4b32d1]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.TCPMessageProcessor@6f695077 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@5b31a9e3]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@7d4b32d1]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@4d91d2da]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@5b31a9e3]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@4d91d2da]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 434]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK70c
From: <sip:34020000001110022400@**********>;tag=00002b98
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 000074970000735A@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387809405"
isSender="false" 
transactionId="z9hg4bk70c" 
callId="000074970000735A@***********" 
firstLine="REGISTER sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK70c
From: <sip:34020000001110022400@**********>;tag=00002b98
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 000074970000735A@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk70c]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK70c]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( REGISTER:z9hG4bK70c):gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for REGISTER sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@9232772 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process REGISTER sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK70c]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb branchID = z9hG4bK70c isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK70c
From: <sip:34020000001110022400@**********>;tag=00002b98
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 000074970000735A@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(000074970000735a@***********:00002b98) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 000074970000735a@***********:00002b98]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [REGISTER transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@50575db8]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@50575db8]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@18ec3f18]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing REGISTER sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@18ec3f18nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@50575db8]source = gov.nist.javax.sip.SipProviderImpl@50575db8]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : REGISTER sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk70c existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk70c]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk70c existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk70c]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk70c]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(000074970000735a@***********:00002b98) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb branchID = z9hG4bK70c isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:228][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:228][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK70c
From: <sip:34020000001110022400@**********>;tag=00002b98
To: <sip:34020000001110022400@**********>
Call-ID: 000074970000735A@***********
CSeq: 1 REGISTER
Date: Thu, 28 Aug 2025 13:30:09 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK70c
From: <sip:34020000001110022400@**********>;tag=00002b98
To: <sip:34020000001110022400@**********>
Call-ID: 000074970000735A@***********
CSeq: 1 REGISTER
Date: Thu, 28 Aug 2025 13:30:09 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387809734"
isSender="true" 
transactionId="z9hg4bk70c" 
callId="000074970000735A@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK70c
From: <sip:34020000001110022400@**********>;tag=00002b98
To: <sip:34020000001110022400@**********>
Call-ID: 000074970000735A@***********
CSeq: 1 REGISTER
Date: Thu, 28 Aug 2025 13:30:09 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk70c]
DEBUG - gov.nist.javax.sip.SipProviderImpl.getNewClientTransaction(SipProviderImpl.java:333) [could not find existing transaction for INVITE sip:34020000001110022400@***********:15090 SIP/2.0
 creating a new one ]
DEBUG - gov.nist.javax.sip.stack.DefaultRouter.getNextHop(DefaultRouter.java:235) [Used request-URI for nextHop = ***********:15090/TCP]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(503d4c3fa88fb3a6f22966ded6da5117@0:0:0:0:0:0:0:0:zlm1756387811919) : returning null]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.<init>(TCPMessageChannel.java:167) [creating new TCPMessageChannel ]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[TCPMessageChannel.java:168][TCPMessageProcessor.java:225][SIPTransactionStack.java:1224][SipProviderImpl.java:379][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.TCPMessageProcessor.createMessageChannel(TCPMessageProcessor.java:233) [key tcp:***********:15090]
DEBUG - gov.nist.javax.sip.stack.TCPMessageProcessor.createMessageChannel(TCPMessageProcessor.java:235) [Creating gov.nist.javax.sip.stack.TCPMessageChannel@df50c13]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.<init>(SIPTransaction.java:352) [use count for encapsulated channelgov.nist.javax.sip.stack.SIPClientTransaction@ffffffff 1]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.<init>(SIPClientTransaction.java:301) [Creating clientTransaction gov.nist.javax.sip.stack.SIPClientTransaction@ffffffff]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPClientTransaction.java:302][SIPTransactionStack.java:1253][SIPTransactionStack.java:1231][SipProviderImpl.java:379][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK1b711a437cc60cdb5aac92bdae595eed]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1417) [ putTransactionHash :  key = z9hg4bk1b711a437cc60cdb5aac92bdae595eed]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK1b711a437cc60cdb5aac92bdae595eed]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setRemoteParty(SIPDialog.java:443) [settingRemoteParty <sip:34020000001110022400@***********:15090>]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLocalSequenceNumber(SIPDialog.java:1247) [setLocalSequenceNumber: original 	0 new  = 1]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1167) [Transaction Added gov.nist.javax.sip.stack.SIPDialog@3d96b0c7zlm1756387811919/null]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1169) [TID = z9hg4bk1b711a437cc60cdb5aac92bdae595eed/false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:1172][SIPDialog.java:327][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:329) [Creating a dialog : gov.nist.javax.sip.stack.SIPDialog@3d96b0c7]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:330) [provider port = 15060]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:332][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setDialog(SIPClientTransaction.java:1521) [setDialog: 503d4c3fa88fb3a6f22966ded6da5117@0:0:0:0:0:0:0:0:zlm1756387811919sipDialog = gov.nist.javax.sip.stack.SIPDialog@3d96b0c7]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendRequest(SIPClientTransaction.java:918) [sendRequest() INVITE sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: 503d4c3fa88fb3a6f22966ded6da5117@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387811919
To: <sip:34020000001110022400@***********:15090>
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK1b711a437cc60cdb5aac92bdae595eed
Max-Forwards: 70
Contact: <sip:**********2000000001@***********:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:**********,**********2000000001:1
Content-Type: application/sdp
Content-Length: 317

v=0
o=**********2000000001 0 0 IN IP4 ***********
s=Play
u=**********2000000001:0
c=IN IP4 ***********
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=**********
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:424) [Sending Message INVITE sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: 503d4c3fa88fb3a6f22966ded6da5117@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387811919
To: <sip:34020000001110022400@***********:15090>
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK1b711a437cc60cdb5aac92bdae595eed
Max-Forwards: 70
Contact: <sip:**********2000000001@***********:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:**********,**********2000000001:1
Content-Type: application/sdp
Content-Length: 317

v=0
o=**********2000000001 0 0 IN IP4 ***********
s=Play
u=**********2000000001:0
c=IN IP4 ***********
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=**********
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:426) [TransactionState null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK1b711a437cc60cdb5aac92bdae595eed]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Calling Transaction gov.nist.javax.sip.stack.SIPClientTransaction@48be35a9 branchID = z9hG4bK1b711a437cc60cdb5aac92bdae595eed isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:465][SIPClientTransaction.java:983][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPClientTransaction@48be35a9 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:139) [sendBytes TCP inAddr *********** port = 15090 length = 897]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:163) [inaddr = /***********]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:165) [port = 15090]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@48be35a9 branchID = z9hG4bK1b711a437cc60cdb5aac92bdae595eed isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:489][SIPClientTransaction.java:983][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@48be35a9 branchID = z9hG4bK1b711a437cc60cdb5aac92bdae595eed isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:986][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk70c]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:201) [removing  = gov.nist.javax.sip.stack.SIPClientTransaction@48be35a9 isReliable true]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk1b711a437cc60cdb5aac92bdae595eed transaction = gov.nist.javax.sip.stack.SIPClientTransaction@48be35a9]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1352) [REMOVED client tx gov.nist.javax.sip.stack.SIPClientTransaction@48be35a9 KEY = z9hg4bk1b711a437cc60cdb5aac92bdae595eed]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1364][SIPClientTransaction.java:206][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@f5d71d]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:258) [Client Use Count = 1]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@f5d71dnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]source = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = gov.nist.javax.sip.stack.SIPClientTransaction@48be35a9]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = null]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.fireTimeoutTimer(SIPServerTransaction.java:1202) [SIPServerTransaction.fireTimeoutTimer this = gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb current state = Completed Transaction method = REGISTER]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb branchID = z9hG4bK70c isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:1227][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk70c transaction = gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk70c]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@50575db8]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@50575db8]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@e323973]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@e323973nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@50575db8]source = gov.nist.javax.sip.SipProviderImpl@50575db8]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk70c]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk70c]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk70c transaction = gov.nist.javax.sip.stack.SIPServerTransaction@48ab29cb]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk70c]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 563]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>1</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387869409"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( MESSAGE:z9hG4bK00004823):gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for MESSAGE sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@4a661095 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>1</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00004ae100006784@***********:000018be]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [MESSAGE transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@50575db8]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@50575db8]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@4e598d01]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@4e598d01nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@50575db8]source = gov.nist.javax.sip.SipProviderImpl@50575db8]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : MESSAGE sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Date: Thu, 28 Aug 2025 13:31:09 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Date: Thu, 28 Aug 2025 13:31:09 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387869758"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Date: Thu, 28 Aug 2025 13:31:09 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.fireTimeoutTimer(SIPServerTransaction.java:1202) [SIPServerTransaction.fireTimeoutTimer this = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 current state = Completed Transaction method = MESSAGE]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:1227][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@50575db8]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@50575db8]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@4f0f3348]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@4f0f3348nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@50575db8]source = gov.nist.javax.sip.SipProviderImpl@50575db8]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 563]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>2</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387929409"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( MESSAGE:z9hG4bK00004823):gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for MESSAGE sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@68556bd8 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>2</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00004ae100006784@***********:000018be]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [MESSAGE transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@50575db8]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@50575db8]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@669288a2]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@669288a2nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@50575db8]source = gov.nist.javax.sip.SipProviderImpl@50575db8]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : MESSAGE sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Date: Thu, 28 Aug 2025 13:32:09 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Date: Thu, 28 Aug 2025 13:32:09 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387929763"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Date: Thu, 28 Aug 2025 13:32:09 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 431]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK78b
From: <sip:34020000001110022400@**********>;tag=00002b98
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 000074970000735A@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387936722"
isSender="false" 
transactionId="z9hg4bk78b" 
callId="000074970000735A@***********" 
firstLine="REGISTER sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK78b
From: <sip:34020000001110022400@**********>;tag=00002b98
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 000074970000735A@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk78b]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK78b]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( REGISTER:z9hG4bK78b):gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for REGISTER sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@56c6aedf messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process REGISTER sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK78b]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2 branchID = z9hG4bK78b isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK78b
From: <sip:34020000001110022400@**********>;tag=00002b98
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 000074970000735A@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(000074970000735a@***********:00002b98) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 000074970000735a@***********:00002b98]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [REGISTER transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@50575db8]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@50575db8]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@58014a77]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing REGISTER sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@58014a77nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@50575db8]source = gov.nist.javax.sip.SipProviderImpl@50575db8]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : REGISTER sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk78b existing={z9hg4bk00004823=gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk78b]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk78b existing={z9hg4bk00004823=gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk78b]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk78b]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(000074970000735a@***********:00002b98) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2 branchID = z9hG4bK78b isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:228][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:228][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK78b
From: <sip:34020000001110022400@**********>;tag=00002b98
To: <sip:34020000001110022400@**********>
Call-ID: 000074970000735A@***********
CSeq: 2 REGISTER
Date: Thu, 28 Aug 2025 13:32:17 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK78b
From: <sip:34020000001110022400@**********>;tag=00002b98
To: <sip:34020000001110022400@**********>
Call-ID: 000074970000735A@***********
CSeq: 2 REGISTER
Date: Thu, 28 Aug 2025 13:32:17 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387937565"
isSender="true" 
transactionId="z9hg4bk78b" 
callId="000074970000735A@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK78b
From: <sip:34020000001110022400@**********>;tag=00002b98
To: <sip:34020000001110022400@**********>
Call-ID: 000074970000735A@***********
CSeq: 2 REGISTER
Date: Thu, 28 Aug 2025 13:32:17 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk78b]
DEBUG - gov.nist.javax.sip.SipProviderImpl.getNewClientTransaction(SipProviderImpl.java:333) [could not find existing transaction for INVITE sip:34020000001110022400@***********:15090 SIP/2.0
 creating a new one ]
DEBUG - gov.nist.javax.sip.stack.DefaultRouter.getNextHop(DefaultRouter.java:235) [Used request-URI for nextHop = ***********:15090/TCP]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(c3a182454f79d4e54d3ef1288b2be765@0:0:0:0:0:0:0:0:zlm1756387939690) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.<init>(SIPTransaction.java:352) [use count for encapsulated channelgov.nist.javax.sip.stack.SIPClientTransaction@ffffffff 2]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.<init>(SIPClientTransaction.java:301) [Creating clientTransaction gov.nist.javax.sip.stack.SIPClientTransaction@ffffffff]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPClientTransaction.java:302][SIPTransactionStack.java:1253][SIPTransactionStack.java:1231][SipProviderImpl.java:379][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK01550539c0ae2c0c91636c32ed251741]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1417) [ putTransactionHash :  key = z9hg4bk01550539c0ae2c0c91636c32ed251741]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK01550539c0ae2c0c91636c32ed251741]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setRemoteParty(SIPDialog.java:443) [settingRemoteParty <sip:34020000001110022400@***********:15090>]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLocalSequenceNumber(SIPDialog.java:1247) [setLocalSequenceNumber: original 	0 new  = 1]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1167) [Transaction Added gov.nist.javax.sip.stack.SIPDialog@7c60d059zlm1756387939690/null]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1169) [TID = z9hg4bk01550539c0ae2c0c91636c32ed251741/false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:1172][SIPDialog.java:327][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:329) [Creating a dialog : gov.nist.javax.sip.stack.SIPDialog@7c60d059]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:330) [provider port = 15060]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:332][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setDialog(SIPClientTransaction.java:1521) [setDialog: c3a182454f79d4e54d3ef1288b2be765@0:0:0:0:0:0:0:0:zlm1756387939690sipDialog = gov.nist.javax.sip.stack.SIPDialog@7c60d059]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendRequest(SIPClientTransaction.java:918) [sendRequest() INVITE sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: c3a182454f79d4e54d3ef1288b2be765@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387939690
To: <sip:34020000001110022400@***********:15090>
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK01550539c0ae2c0c91636c32ed251741
Max-Forwards: 70
Contact: <sip:**********2000000001@***********:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:**********,**********2000000001:1
Content-Type: application/sdp
Content-Length: 317

v=0
o=**********2000000001 0 0 IN IP4 ***********
s=Play
u=**********2000000001:0
c=IN IP4 ***********
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=**********
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:424) [Sending Message INVITE sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: c3a182454f79d4e54d3ef1288b2be765@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387939690
To: <sip:34020000001110022400@***********:15090>
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK01550539c0ae2c0c91636c32ed251741
Max-Forwards: 70
Contact: <sip:**********2000000001@***********:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:**********,**********2000000001:1
Content-Type: application/sdp
Content-Length: 317

v=0
o=**********2000000001 0 0 IN IP4 ***********
s=Play
u=**********2000000001:0
c=IN IP4 ***********
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=**********
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:426) [TransactionState null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK01550539c0ae2c0c91636c32ed251741]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Calling Transaction gov.nist.javax.sip.stack.SIPClientTransaction@b1950426 branchID = z9hG4bK01550539c0ae2c0c91636c32ed251741 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:465][SIPClientTransaction.java:983][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPClientTransaction@b1950426 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:139) [sendBytes TCP inAddr *********** port = 15090 length = 897]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:163) [inaddr = /***********]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:165) [port = 15090]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@b1950426 branchID = z9hG4bK01550539c0ae2c0c91636c32ed251741 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:489][SIPClientTransaction.java:983][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@b1950426 branchID = z9hG4bK01550539c0ae2c0c91636c32ed251741 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:986][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk78b]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:201) [removing  = gov.nist.javax.sip.stack.SIPClientTransaction@b1950426 isReliable true]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk01550539c0ae2c0c91636c32ed251741 transaction = gov.nist.javax.sip.stack.SIPClientTransaction@b1950426]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1352) [REMOVED client tx gov.nist.javax.sip.stack.SIPClientTransaction@b1950426 KEY = z9hg4bk01550539c0ae2c0c91636c32ed251741]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1364][SIPClientTransaction.java:206][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@616c488b]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:258) [Client Use Count = 2]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@616c488bnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]source = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = gov.nist.javax.sip.stack.SIPClientTransaction@b1950426]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = null]
DEBUG - gov.nist.javax.sip.stack.TCPMessageProcessor.run(TCPMessageProcessor.java:142) [Accepting new connection!]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.<init>(TCPMessageChannel.java:129) [creating new TCPMessageChannel ]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[TCPMessageChannel.java:130][TCPMessageProcessor.java:147][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.TCPMessageProcessor.cacheMessageChannel(TCPMessageProcessor.java:249) [Closing tcp:***********:15090]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.close(TCPMessageChannel.java:200) [Closing message Channel gov.nist.javax.sip.stack.TCPMessageChannel@df50c13]
DEBUG - gov.nist.javax.sip.stack.TCPMessageProcessor.cacheMessageChannel(TCPMessageProcessor.java:253) [Caching tcp:***********:15090]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.processMessage(TCPMessageChannel.java:485) [----Processing Message---]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk795]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.<init>(SIPTransaction.java:352) [use count for encapsulated channelgov.nist.javax.sip.stack.SIPServerTransaction@ffffffff 1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][TCPMessageChannel.java:503][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK795]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( REGISTER:z9hG4bK795):gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for REGISTER sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@2ae4087a messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][TCPMessageChannel.java:503][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.processMessage(TCPMessageChannel.java:506) [---- sipServerRequest = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK795]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4 branchID = z9hG4bK795 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][TCPMessageChannel.java:512][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK795
From: <sip:34020000001110022400@**********>;tag=00002d57
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00004CB700006B6D@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4 listening point = 0.0.0.0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004cb700006b6d@***********:00002d57) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00004cb700006b6d@***********:00002d57]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [REGISTER transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][TCPMessageChannel.java:512][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@2edb89fe]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@2edb89fenevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]source = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : REGISTER sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk795 existing={z9hg4bk78b=gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2, z9hg4bk00004823=gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk795]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4]
INFO - <message
from="***********:15090" 
to="0.0.0.0:15060" 
time="1756387946644"
isSender="false" 
transactionId="z9hg4bk795" 
callId="00004CB700006B6D@***********" 
firstLine="REGISTER sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK795
From: <sip:34020000001110022400@**********>;tag=00002d57
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00004CB700006B6D@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk795 existing={z9hg4bk78b=gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2, z9hg4bk00004823=gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk795]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk795]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004cb700006b6d@***********:00002d57) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4 branchID = z9hG4bK795 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:971][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:228][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4 getState = Terminated Transaction]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:139) [sendBytes TCP inAddr *********** port = 15090 length = 349]
INFO - <message
from="0.0.0.0:15060" 
to="***********:15090" 
time="1756387947169"
isSender="true" 
transactionId="z9hg4bk795" 
callId="00004CB700006B6D@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK795
From: <sip:34020000001110022400@**********>;tag=00002d57
To: <sip:34020000001110022400@**********>
Call-ID: 00004CB700006B6D@***********
CSeq: 1 REGISTER
Date: Thu, 28 Aug 2025 13:32:27 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk795]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk795]
DEBUG - gov.nist.javax.sip.SipProviderImpl.getNewClientTransaction(SipProviderImpl.java:333) [could not find existing transaction for INVITE sip:34020000001110022400@***********:15090 SIP/2.0
 creating a new one ]
DEBUG - gov.nist.javax.sip.stack.DefaultRouter.getNextHop(DefaultRouter.java:235) [Used request-URI for nextHop = ***********:15090/TCP]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0:zlm1756387949554) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.<init>(SIPTransaction.java:352) [use count for encapsulated channelgov.nist.javax.sip.stack.SIPClientTransaction@ffffffff 2]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.<init>(SIPClientTransaction.java:301) [Creating clientTransaction gov.nist.javax.sip.stack.SIPClientTransaction@ffffffff]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPClientTransaction.java:302][SIPTransactionStack.java:1253][SIPTransactionStack.java:1231][SipProviderImpl.java:379][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK778238729c1ea591c739d271d33b02ef]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1417) [ putTransactionHash :  key = z9hg4bk778238729c1ea591c739d271d33b02ef]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK778238729c1ea591c739d271d33b02ef]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setRemoteParty(SIPDialog.java:443) [settingRemoteParty <sip:34020000001110022400@***********:15090>]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLocalSequenceNumber(SIPDialog.java:1247) [setLocalSequenceNumber: original 	0 new  = 1]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1167) [Transaction Added gov.nist.javax.sip.stack.SIPDialog@52618341zlm1756387949554/null]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1169) [TID = z9hg4bk778238729c1ea591c739d271d33b02ef/false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:1172][SIPDialog.java:327][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:329) [Creating a dialog : gov.nist.javax.sip.stack.SIPDialog@52618341]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:330) [provider port = 15060]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:332][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setDialog(SIPClientTransaction.java:1521) [setDialog: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0:zlm1756387949554sipDialog = gov.nist.javax.sip.stack.SIPDialog@52618341]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendRequest(SIPClientTransaction.java:918) [sendRequest() INVITE sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387949554
To: <sip:34020000001110022400@***********:15090>
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK778238729c1ea591c739d271d33b02ef
Max-Forwards: 70
Contact: <sip:**********2000000001@***********:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:**********,**********2000000001:1
Content-Type: application/sdp
Content-Length: 317

v=0
o=**********2000000001 0 0 IN IP4 ***********
s=Play
u=**********2000000001:0
c=IN IP4 ***********
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=**********
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:424) [Sending Message INVITE sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387949554
To: <sip:34020000001110022400@***********:15090>
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK778238729c1ea591c739d271d33b02ef
Max-Forwards: 70
Contact: <sip:**********2000000001@***********:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:**********,**********2000000001:1
Content-Type: application/sdp
Content-Length: 317

v=0
o=**********2000000001 0 0 IN IP4 ***********
s=Play
u=**********2000000001:0
c=IN IP4 ***********
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=**********
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:426) [TransactionState null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK778238729c1ea591c739d271d33b02ef]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Calling Transaction gov.nist.javax.sip.stack.SIPClientTransaction@f1533806 branchID = z9hG4bK778238729c1ea591c739d271d33b02ef isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:465][SIPClientTransaction.java:983][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPClientTransaction@f1533806 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:139) [sendBytes TCP inAddr *********** port = 15090 length = 897]
INFO - <message
from="0.0.0.0:15060" 
to="***********:15090" 
time="1756387949555"
isSender="true" 
transactionId="z9hg4bk778238729c1ea591c739d271d33b02ef" 
callId="7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0" 
firstLine="INVITE sip:34020000001110022400@***********:15090 SIP/2.0" 
>
<![CDATA[INVITE sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387949554
To: <sip:34020000001110022400@***********:15090>
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK778238729c1ea591c739d271d33b02ef
Max-Forwards: 70
Contact: <sip:**********2000000001@***********:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:**********,**********2000000001:1
Content-Type: application/sdp
Content-Length: 317

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPClientTransaction@f1533806]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1171][TCPMessageChannel.java:570][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
INFO - <message
from="***********:15090" 
to="0.0.0.0:15060" 
time="1756387949556"
isSender="false" 
transactionId="z9hg4bk778238729c1ea591c739d271d33b02ef" 
callId="7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0" 
firstLine="SIP/2.0 100 Trying" 
>
<![CDATA[SIP/2.0 100 Trying
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK778238729c1ea591c739d271d33b02ef
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387949554
To: <sip:34020000001110022400@***********:15090>;tag=00004ae1
Call-ID: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:819) [clientTx: looking for key z9hg4bk778238729c1ea591c739d271d33b02ef]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerResponse(NistSipMessageFactoryImpl.java:108) [Found Transaction gov.nist.javax.sip.stack.SIPClientTransaction@f1533806 for SIP/2.0 100 Trying
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK778238729c1ea591c739d271d33b02ef
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387949554
To: <sip:34020000001110022400@***********:15090>;tag=00004ae1
Call-ID: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setResponseInterface(SIPClientTransaction.java:316) [Setting response interface for gov.nist.javax.sip.stack.SIPClientTransaction@f1533806 to gov.nist.javax.sip.DialogFilter@289e3956]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.processResponse(SIPClientTransaction.java:527) [processing SIP/2.0 100 Trying
current state = Calling Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.processResponse(SIPClientTransaction.java:530) [dialog = gov.nist.javax.sip.stack.SIPDialog@52618341]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Proceeding Transaction gov.nist.javax.sip.stack.SIPClientTransaction@f1533806 branchID = z9hG4bK778238729c1ea591c739d271d33b02ef isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:799][SIPClientTransaction.java:547][SIPClientTransaction.java:1470][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.DialogFilter.processResponse(DialogFilter.java:1023) [PROCESSING INCOMING RESPONSESIP/2.0 100 Trying
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK778238729c1ea591c739d271d33b02ef
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387949554
To: <sip:34020000001110022400@***********:15090>;tag=00004ae1
Call-ID: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.DialogFilter.processResponse(DialogFilter.java:1055) [Transaction = gov.nist.javax.sip.stack.SIPClientTransaction@f1533806]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.ResponseEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:145) [Dialog = gov.nist.javax.sip.stack.SIPDialog@52618341]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1139][SIPClientTransaction.java:802][SIPClientTransaction.java:547][SIPClientTransaction.java:1470][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@bf6fd0e]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPClientTransaction@f1533806]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][TCPMessageChannel.java:589][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPClientTransaction@f1533806]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1171][TCPMessageChannel.java:570][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
INFO - <message
from="***********:15090" 
to="0.0.0.0:15060" 
time="1756387949556"
isSender="false" 
transactionId="z9hg4bk778238729c1ea591c739d271d33b02ef" 
callId="7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK778238729c1ea591c739d271d33b02ef
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387949554
To: <sip:34020000001110022400@***********:15090>;tag=00004ae1
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: application/sdp
Content-Length: 180

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:819) [clientTx: looking for key z9hg4bk778238729c1ea591c739d271d33b02ef]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerResponse(NistSipMessageFactoryImpl.java:108) [Found Transaction gov.nist.javax.sip.stack.SIPClientTransaction@f1533806 for SIP/2.0 200 OK
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK778238729c1ea591c739d271d33b02ef
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387949554
To: <sip:34020000001110022400@***********:15090>;tag=00004ae1
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: application/sdp
Content-Length: 180

v=0
o=34020000001110022400 0 0 IN IP4 ***********
s=Play
c=IN IP4 ***********
t=0 0
m=video 20002 RTP/AVP 96
a=rtpmap:96 PS/90000
a=sendonly
y=**********
f=v/2////a/6//1
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setResponseInterface(SIPClientTransaction.java:316) [Setting response interface for gov.nist.javax.sip.stack.SIPClientTransaction@f1533806 to gov.nist.javax.sip.DialogFilter@768f72cc]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2214) [sipDialog: setLastResponse:gov.nist.javax.sip.stack.SIPDialog@52618341 lastResponse = SIP/2.0 200 OK
]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:2229][SIPClientTransaction.java:1424][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2230) [cseqMethod = INVITE]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2231) [dialogState = null]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2233) [method = INVITE]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2234) [statusCode = 200]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2235) [transaction = gov.nist.javax.sip.stack.SIPClientTransaction@f1533806]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setRemoteTag(SIPDialog.java:1184) [setRemoteTag(): gov.nist.javax.sip.stack.SIPDialog@52618341 remoteTag = null new tag = 00004ae1]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putDialog(SIPTransactionStack.java:530) [putDialog dialogId=7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0:zlm1756387949554:00004ae1 dialog = gov.nist.javax.sip.stack.SIPDialog@52618341]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:535][SIPDialog.java:2268][SIPClientTransaction.java:1424][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addRoute(SIPDialog.java:548) [setContact: dialogState: gov.nist.javax.sip.stack.SIPDialog@52618341state = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:596][SIPDialog.java:2269][SIPClientTransaction.java:1424][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setState(SIPDialog.java:870) [Setting dialog state for gov.nist.javax.sip.stack.SIPDialog@52618341newState = 1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:872][SIPDialog.java:2271][SIPClientTransaction.java:1424][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setState(SIPDialog.java:875) [gov.nist.javax.sip.stack.SIPDialog@52618341  old dialog state is null]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setState(SIPDialog.java:877) [gov.nist.javax.sip.stack.SIPDialog@52618341  New dialog state is Confirmed Dialog]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setDialog(SIPClientTransaction.java:1521) [setDialog: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0:zlm1756387949554:00004ae1sipDialog = gov.nist.javax.sip.stack.SIPDialog@52618341]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.processResponse(SIPClientTransaction.java:527) [processing SIP/2.0 200 OK
current state = Proceeding Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.processResponse(SIPClientTransaction.java:530) [dialog = gov.nist.javax.sip.stack.SIPDialog@52618341]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@f1533806 branchID = z9hG4bK778238729c1ea591c739d271d33b02ef isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:855][SIPClientTransaction.java:547][SIPClientTransaction.java:1470][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.DialogFilter.processResponse(DialogFilter.java:1023) [PROCESSING INCOMING RESPONSESIP/2.0 200 OK
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK778238729c1ea591c739d271d33b02ef
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387949554
To: <sip:34020000001110022400@***********:15090>;tag=00004ae1
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: application/sdp
Content-Length: 180

]
DEBUG - gov.nist.javax.sip.DialogFilter.processResponse(DialogFilter.java:1055) [Transaction = gov.nist.javax.sip.stack.SIPClientTransaction@f1533806]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2214) [sipDialog: setLastResponse:gov.nist.javax.sip.stack.SIPDialog@52618341 lastResponse = SIP/2.0 200 OK
]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:2229][DialogFilter.java:1135][SIPClientTransaction.java:857][SIPClientTransaction.java:547][SIPClientTransaction.java:1470][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2230) [cseqMethod = INVITE]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2231) [dialogState = Confirmed Dialog]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2233) [method = INVITE]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2234) [statusCode = 200]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2235) [transaction = gov.nist.javax.sip.stack.SIPClientTransaction@f1533806]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setDialog(SIPClientTransaction.java:1521) [setDialog: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0:zlm1756387949554:00004ae1sipDialog = gov.nist.javax.sip.stack.SIPDialog@52618341]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.ResponseEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:145) [Dialog = gov.nist.javax.sip.stack.SIPDialog@52618341]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1139][SIPClientTransaction.java:857][SIPClientTransaction.java:547][SIPClientTransaction.java:1470][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@22c0868]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPClientTransaction@f1533806]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][TCPMessageChannel.java:589][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:201) [removing  = gov.nist.javax.sip.stack.SIPClientTransaction@f1533806 isReliable true]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk778238729c1ea591c739d271d33b02ef transaction = gov.nist.javax.sip.stack.SIPClientTransaction@f1533806]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1352) [REMOVED client tx gov.nist.javax.sip.stack.SIPClientTransaction@f1533806 KEY = z9hg4bk778238729c1ea591c739d271d33b02ef]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1364][SIPClientTransaction.java:206][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@7e0a1c74]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:258) [Client Use Count = 2]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk795]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@bf6fd0enevents 3]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.ResponseEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]source = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:281) [Calling listener for SIP/2.0 100 Trying
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPClientTransaction@f1533806]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][EventScanner.java:362][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@22c0868nevents 3]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.ResponseEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]source = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:281) [Calling listener for SIP/2.0 200 OK
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0:zlm1756387949554:00004ae1) : returning gov.nist.javax.sip.stack.SIPDialog@52618341]
WARN - Dialog exists -- you may want to use Dialog.sendAck() Confirmed Dialog
DEBUG - gov.nist.javax.sip.stack.DefaultRouter.getNextHop(DefaultRouter.java:235) [Used request-URI for nextHop = ***********:15090/TCP]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:139) [sendBytes TCP inAddr *********** port = 15090 length = 499]
INFO - <message
from="0.0.0.0:15060" 
to="***********:15090" 
time="1756387950072"
isSender="true" 
transactionId="z9hg4bk778238729c1ea591c739d271d33b02ef" 
callId="7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0" 
firstLine="ACK sip:34020000001110022400@***********:15090 SIP/2.0" 
>
<![CDATA[ACK sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0
CSeq: 1 ACK
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387949554
To: <sip:34020000001110022400@***********:15090>;tag=00004ae1
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK778238729c1ea591c739d271d33b02ef
Max-Forwards: 70
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:**********,**********2000000001:1
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.SipProviderImpl.sendRequest(SipProviderImpl.java:699) [done sending ACK to hop ***********:15090/TCP]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:331) [Garbage collecting unacknowledged dialog]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPClientTransaction@f1533806]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][EventScanner.java:362][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@7e0a1c74nevents 3]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]source = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = gov.nist.javax.sip.stack.SIPClientTransaction@f1533806]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk795]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk795 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk795]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@1983390c]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@1983390cnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]source = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ab4]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.processMessage(TCPMessageChannel.java:485) [----Processing Message---]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk7a3]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.<init>(SIPTransaction.java:352) [use count for encapsulated channelgov.nist.javax.sip.stack.SIPServerTransaction@ffffffff 3]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][TCPMessageChannel.java:503][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK7a3]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( REGISTER:z9hG4bK7a3):gov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for REGISTER sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@52d28136 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][TCPMessageChannel.java:503][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.processMessage(TCPMessageChannel.java:506) [---- sipServerRequest = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK7a3]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a branchID = z9hG4bK7a3 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][TCPMessageChannel.java:512][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK7a3
From: <sip:34020000001110022400@**********>;tag=00002d57
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00004CB700006B6D@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a listening point = 0.0.0.0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004cb700006b6d@***********:00002d57) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00004cb700006b6d@***********:00002d57]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [REGISTER transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][TCPMessageChannel.java:512][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@3512a3c2]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@3512a3c2nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]source = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : REGISTER sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a sipEvent.serverTx = null]
INFO - <message
from="***********:15090" 
to="0.0.0.0:15060" 
time="1756387960494"
isSender="false" 
transactionId="z9hg4bk7a3" 
callId="00004CB700006B6D@***********" 
firstLine="REGISTER sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK7a3
From: <sip:34020000001110022400@**********>;tag=00002d57
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00004CB700006B6D@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk7a3 existing={z9hg4bk78b=gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2, z9hg4bk00004823=gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk7a3]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk7a3 existing={z9hg4bk78b=gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2, z9hg4bk00004823=gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk7a3]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk7a3]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004cb700006b6d@***********:00002d57) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a branchID = z9hG4bK7a3 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:971][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:228][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a getState = Terminated Transaction]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:139) [sendBytes TCP inAddr *********** port = 15090 length = 349]
INFO - <message
from="0.0.0.0:15060" 
to="***********:15090" 
time="1756387961095"
isSender="true" 
transactionId="z9hg4bk7a3" 
callId="00004CB700006B6D@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK7a3
From: <sip:34020000001110022400@**********>;tag=00002d57
To: <sip:34020000001110022400@**********>
Call-ID: 00004CB700006B6D@***********
CSeq: 2 REGISTER
Date: Thu, 28 Aug 2025 13:32:41 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk7a3]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.run(TCPMessageChannel.java:655) [IOException  closing sock java.net.SocketException: Connection reset]
DEBUG - gov.nist.javax.sip.stack.TCPMessageProcessor.remove(TCPMessageProcessor.java:206) [Thread[TCPMessageChannelThread,5,main] removing tcp:***********:15090]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk7a3]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.fireTimeoutTimer(SIPServerTransaction.java:1202) [SIPServerTransaction.fireTimeoutTimer this = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 current state = Completed Transaction method = MESSAGE]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:1227][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@50575db8]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@50575db8]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@2f20cc07]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk7a3]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@2f20cc07nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@50575db8]source = gov.nist.javax.sip.SipProviderImpl@50575db8]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk7a3]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk7a3 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk7a3]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@2c0be178]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@2c0be178nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]source = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2f8a]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.fireTimeoutTimer(SIPServerTransaction.java:1202) [SIPServerTransaction.fireTimeoutTimer this = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2 current state = Completed Transaction method = REGISTER]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2 branchID = z9hG4bK78b isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:1227][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk78b transaction = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk78b]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@50575db8]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@50575db8]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@24ca131]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@24ca131nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@50575db8]source = gov.nist.javax.sip.SipProviderImpl@50575db8]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk78b]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk78b]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk78b transaction = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2ac2]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk78b]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setState(SIPDialog.java:870) [Setting dialog state for gov.nist.javax.sip.stack.SIPDialog@52618341newState = 3]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:872][SIPDialog.java:1374][SIPDialog.java:286][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setState(SIPDialog.java:875) [gov.nist.javax.sip.stack.SIPDialog@52618341  old dialog state is Confirmed Dialog]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setState(SIPDialog.java:877) [gov.nist.javax.sip.stack.SIPDialog@52618341  New dialog state is Terminated Dialog]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.DialogTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]currentTransaction = nullthis.sipListener = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:635][SIPDialog.java:191][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@2b6e178e]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@2b6e178enevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.DialogTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@7a643873]source = gov.nist.javax.sip.SipProviderImpl@7a643873]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=GB28181-1756388258158, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=sipdebug.log}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="GB28181-1756388258158"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = UDP]
ERROR - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:772) [Invalid argument address = 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.stop(SipStackImpl.java:931) [stopStack -- stoppping the stack]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:455) [Stopped event scanner!!]
DEBUG - gov.nist.javax.sip.SipStackImpl.stop(SipStackImpl.java:931) [stopStack -- stoppping the stack]
DEBUG - gov.nist.javax.sip.stack.UDPMessageProcessor.run(UDPMessageProcessor.java:251) [UDPMessageProcessor: Stopping]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=GB28181-1756388332247, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=sipdebug.log}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="GB28181-1756388332247"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@4850d66b listeningPoint = gov.nist.javax.sip.ListeningPointImpl@21f479cd]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.TCPMessageProcessor@22846a1d listeningPoint = gov.nist.javax.sip.ListeningPointImpl@fe38c0e]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@21f479cd]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@121dac1a]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@fe38c0e]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@121dac1a]
DEBUG - gov.nist.javax.sip.SipStackImpl.stop(SipStackImpl.java:931) [stopStack -- stoppping the stack]
DEBUG - gov.nist.javax.sip.stack.UDPMessageProcessor.run(UDPMessageProcessor.java:251) [UDPMessageProcessor: Stopping]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=GB28181-1756388382838, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=sipdebug.log}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="GB28181-1756388382838"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@799fc4c9 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@44428583]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.TCPMessageProcessor@48126a17 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@1a7437d8]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@44428583]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@54780c40]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@1a7437d8]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@54780c40]
DEBUG - gov.nist.javax.sip.stack.TCPMessageProcessor.run(TCPMessageProcessor.java:142) [Accepting new connection!]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.<init>(TCPMessageChannel.java:129) [creating new TCPMessageChannel ]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[TCPMessageChannel.java:130][TCPMessageProcessor.java:147][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.TCPMessageProcessor.cacheMessageChannel(TCPMessageProcessor.java:253) [Caching tcp:***********:15090]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.processMessage(TCPMessageChannel.java:485) [----Processing Message---]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk964]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.<init>(SIPTransaction.java:352) [use count for encapsulated channelgov.nist.javax.sip.stack.SIPServerTransaction@ffffffff 1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][TCPMessageChannel.java:503][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK964]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( REGISTER:z9hG4bK964):gov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for REGISTER sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@95481e6 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][TCPMessageChannel.java:503][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.processMessage(TCPMessageChannel.java:506) [---- sipServerRequest = gov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK964]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8 branchID = z9hG4bK964 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][TCPMessageChannel.java:512][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK964
From: <sip:34020000001110023168@**********>;tag=0000333f
To: <sip:34020000001110023168@**********>
Contact: <sip:34020000001110023168@***********:15090>
Call-ID: 000001BE00005AED@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8 listening point = 0.0.0.0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(000001be00005aed@***********:0000333f) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 000001be00005aed@***********:0000333f]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [REGISTER transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][TCPMessageChannel.java:512][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@1982be74]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@1982be74nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]source = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : REGISTER sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk964 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk964]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8]
INFO - <message
from="***********:15090" 
to="0.0.0.0:15060" 
time="1756388409398"
isSender="false" 
transactionId="z9hg4bk964" 
callId="000001BE00005AED@***********" 
firstLine="REGISTER sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK964
From: <sip:34020000001110023168@**********>;tag=0000333f
To: <sip:34020000001110023168@**********>
Contact: <sip:34020000001110023168@***********:15090>
Call-ID: 000001BE00005AED@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk964 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk964]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk964]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(000001be00005aed@***********:0000333f) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8 branchID = z9hG4bK964 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:971][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:228][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8 getState = Terminated Transaction]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:139) [sendBytes TCP inAddr *********** port = 15090 length = 349]
INFO - <message
from="0.0.0.0:15060" 
to="***********:15090" 
time="1756388410259"
isSender="true" 
transactionId="z9hg4bk964" 
callId="000001BE00005AED@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK964
From: <sip:34020000001110023168@**********>;tag=0000333f
To: <sip:34020000001110023168@**********>
Call-ID: 000001BE00005AED@***********
CSeq: 1 REGISTER
Date: Thu, 28 Aug 2025 13:40:10 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk964]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk964]
DEBUG - gov.nist.javax.sip.SipProviderImpl.getNewClientTransaction(SipProviderImpl.java:333) [could not find existing transaction for INVITE sip:34020000001110023168@***********:15090 SIP/2.0
 creating a new one ]
DEBUG - gov.nist.javax.sip.stack.DefaultRouter.getNextHop(DefaultRouter.java:235) [Used request-URI for nextHop = ***********:15090/TCP]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0:zlm1756388412777) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.<init>(SIPTransaction.java:352) [use count for encapsulated channelgov.nist.javax.sip.stack.SIPClientTransaction@ffffffff 2]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.<init>(SIPClientTransaction.java:301) [Creating clientTransaction gov.nist.javax.sip.stack.SIPClientTransaction@ffffffff]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPClientTransaction.java:302][SIPTransactionStack.java:1253][SIPTransactionStack.java:1231][SipProviderImpl.java:379][GB28181SipServer.java:589][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK9981c5ae61503b85f37a497fdf817bee]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1417) [ putTransactionHash :  key = z9hg4bk9981c5ae61503b85f37a497fdf817bee]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK9981c5ae61503b85f37a497fdf817bee]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setRemoteParty(SIPDialog.java:443) [settingRemoteParty <sip:34020000001110023168@***********:15090>]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLocalSequenceNumber(SIPDialog.java:1247) [setLocalSequenceNumber: original 	0 new  = 1]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1167) [Transaction Added gov.nist.javax.sip.stack.SIPDialog@6f0565e8zlm1756388412777/null]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1169) [TID = z9hg4bk9981c5ae61503b85f37a497fdf817bee/false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:1172][SIPDialog.java:327][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:589][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:329) [Creating a dialog : gov.nist.javax.sip.stack.SIPDialog@6f0565e8]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:330) [provider port = 15060]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:332][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:589][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setDialog(SIPClientTransaction.java:1521) [setDialog: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0:zlm1756388412777sipDialog = gov.nist.javax.sip.stack.SIPDialog@6f0565e8]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendRequest(SIPClientTransaction.java:918) [sendRequest() INVITE sip:34020000001110023168@***********:15090 SIP/2.0
Call-ID: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***************:15060>;tag=zlm1756388412777
To: <sip:34020000001110023168@***********:15090>
Via: SIP/2.0/TCP ***************:15060;branch=z9hG4bK9981c5ae61503b85f37a497fdf817bee
Max-Forwards: 70
Contact: <sip:**********2000000001@***************:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110023168:**********,**********2000000001:1
Content-Type: application/sdp
Content-Length: 317

v=0
o=**********2000000001 0 0 IN IP4 ***********
s=Play
u=**********2000000001:0
c=IN IP4 ***********
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=**********
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:424) [Sending Message INVITE sip:34020000001110023168@***********:15090 SIP/2.0
Call-ID: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***************:15060>;tag=zlm1756388412777
To: <sip:34020000001110023168@***********:15090>
Via: SIP/2.0/TCP ***************:15060;branch=z9hG4bK9981c5ae61503b85f37a497fdf817bee
Max-Forwards: 70
Contact: <sip:**********2000000001@***************:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110023168:**********,**********2000000001:1
Content-Type: application/sdp
Content-Length: 317

v=0
o=**********2000000001 0 0 IN IP4 ***********
s=Play
u=**********2000000001:0
c=IN IP4 ***********
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=**********
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:426) [TransactionState null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK9981c5ae61503b85f37a497fdf817bee]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Calling Transaction gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1 branchID = z9hG4bK9981c5ae61503b85f37a497fdf817bee isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:465][SIPClientTransaction.java:983][GB28181SipServer.java:590][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:139) [sendBytes TCP inAddr *********** port = 15090 length = 909]
INFO - <message
from="0.0.0.0:15060" 
to="***********:15090" 
time="1756388412783"
isSender="true" 
transactionId="z9hg4bk9981c5ae61503b85f37a497fdf817bee" 
callId="c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0" 
firstLine="INVITE sip:34020000001110023168@***********:15090 SIP/2.0" 
>
<![CDATA[INVITE sip:34020000001110023168@***********:15090 SIP/2.0
Call-ID: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***************:15060>;tag=zlm1756388412777
To: <sip:34020000001110023168@***********:15090>
Via: SIP/2.0/TCP ***************:15060;branch=z9hG4bK9981c5ae61503b85f37a497fdf817bee
Max-Forwards: 70
Contact: <sip:**********2000000001@***************:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110023168:**********,**********2000000001:1
Content-Type: application/sdp
Content-Length: 317

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1171][TCPMessageChannel.java:570][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
INFO - <message
from="***********:15090" 
to="0.0.0.0:15060" 
time="1756388412784"
isSender="false" 
transactionId="z9hg4bk9981c5ae61503b85f37a497fdf817bee" 
callId="c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0" 
firstLine="SIP/2.0 100 Trying" 
>
<![CDATA[SIP/2.0 100 Trying
Via: SIP/2.0/TCP ***************:15060;branch=z9hG4bK9981c5ae61503b85f37a497fdf817bee
From: <sip:**********2000000001@***************:15060>;tag=zlm1756388412777
To: <sip:34020000001110023168@***********:15090>;tag=00004ae1
Call-ID: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:819) [clientTx: looking for key z9hg4bk9981c5ae61503b85f37a497fdf817bee]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerResponse(NistSipMessageFactoryImpl.java:108) [Found Transaction gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1 for SIP/2.0 100 Trying
Via: SIP/2.0/TCP ***************:15060;branch=z9hG4bK9981c5ae61503b85f37a497fdf817bee
From: <sip:**********2000000001@***************:15060>;tag=zlm1756388412777
To: <sip:34020000001110023168@***********:15090>;tag=00004ae1
Call-ID: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setResponseInterface(SIPClientTransaction.java:316) [Setting response interface for gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1 to gov.nist.javax.sip.DialogFilter@5d6a83e0]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.processResponse(SIPClientTransaction.java:527) [processing SIP/2.0 100 Trying
current state = Calling Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.processResponse(SIPClientTransaction.java:530) [dialog = gov.nist.javax.sip.stack.SIPDialog@6f0565e8]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Proceeding Transaction gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1 branchID = z9hG4bK9981c5ae61503b85f37a497fdf817bee isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:799][SIPClientTransaction.java:547][SIPClientTransaction.java:1470][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.DialogFilter.processResponse(DialogFilter.java:1023) [PROCESSING INCOMING RESPONSESIP/2.0 100 Trying
Via: SIP/2.0/TCP ***************:15060;branch=z9hG4bK9981c5ae61503b85f37a497fdf817bee
From: <sip:**********2000000001@***************:15060>;tag=zlm1756388412777
To: <sip:34020000001110023168@***********:15090>;tag=00004ae1
Call-ID: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.DialogFilter.processResponse(DialogFilter.java:1055) [Transaction = gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.ResponseEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:145) [Dialog = gov.nist.javax.sip.stack.SIPDialog@6f0565e8]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1139][SIPClientTransaction.java:802][SIPClientTransaction.java:547][SIPClientTransaction.java:1470][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@7b1e2115]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][TCPMessageChannel.java:589][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1171][TCPMessageChannel.java:570][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
INFO - <message
from="***********:15090" 
to="0.0.0.0:15060" 
time="1756388412785"
isSender="false" 
transactionId="z9hg4bk9981c5ae61503b85f37a497fdf817bee" 
callId="c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/TCP ***************:15060;branch=z9hG4bK9981c5ae61503b85f37a497fdf817bee
From: <sip:**********2000000001@***************:15060>;tag=zlm1756388412777
To: <sip:34020000001110023168@***********:15090>;tag=00004ae1
Contact: <sip:34020000001110023168@***********:15090>
Call-ID: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: application/sdp
Content-Length: 180

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:819) [clientTx: looking for key z9hg4bk9981c5ae61503b85f37a497fdf817bee]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerResponse(NistSipMessageFactoryImpl.java:108) [Found Transaction gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1 for SIP/2.0 200 OK
Via: SIP/2.0/TCP ***************:15060;branch=z9hG4bK9981c5ae61503b85f37a497fdf817bee
From: <sip:**********2000000001@***************:15060>;tag=zlm1756388412777
To: <sip:34020000001110023168@***********:15090>;tag=00004ae1
Contact: <sip:34020000001110023168@***********:15090>
Call-ID: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: application/sdp
Content-Length: 180

v=0
o=34020000001110023168 0 0 IN IP4 ***********
s=Play
c=IN IP4 ***********
t=0 0
m=video 20002 RTP/AVP 96
a=rtpmap:96 PS/90000
a=sendonly
y=**********
f=v/2////a/6//1
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setResponseInterface(SIPClientTransaction.java:316) [Setting response interface for gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1 to gov.nist.javax.sip.DialogFilter@43069d56]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2214) [sipDialog: setLastResponse:gov.nist.javax.sip.stack.SIPDialog@6f0565e8 lastResponse = SIP/2.0 200 OK
]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:2229][SIPClientTransaction.java:1424][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2230) [cseqMethod = INVITE]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2231) [dialogState = null]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2233) [method = INVITE]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2234) [statusCode = 200]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2235) [transaction = gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setRemoteTag(SIPDialog.java:1184) [setRemoteTag(): gov.nist.javax.sip.stack.SIPDialog@6f0565e8 remoteTag = null new tag = 00004ae1]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putDialog(SIPTransactionStack.java:530) [putDialog dialogId=c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0:zlm1756388412777:00004ae1 dialog = gov.nist.javax.sip.stack.SIPDialog@6f0565e8]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:535][SIPDialog.java:2268][SIPClientTransaction.java:1424][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addRoute(SIPDialog.java:548) [setContact: dialogState: gov.nist.javax.sip.stack.SIPDialog@6f0565e8state = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:596][SIPDialog.java:2269][SIPClientTransaction.java:1424][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setState(SIPDialog.java:870) [Setting dialog state for gov.nist.javax.sip.stack.SIPDialog@6f0565e8newState = 1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:872][SIPDialog.java:2271][SIPClientTransaction.java:1424][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setState(SIPDialog.java:875) [gov.nist.javax.sip.stack.SIPDialog@6f0565e8  old dialog state is null]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setState(SIPDialog.java:877) [gov.nist.javax.sip.stack.SIPDialog@6f0565e8  New dialog state is Confirmed Dialog]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setDialog(SIPClientTransaction.java:1521) [setDialog: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0:zlm1756388412777:00004ae1sipDialog = gov.nist.javax.sip.stack.SIPDialog@6f0565e8]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.processResponse(SIPClientTransaction.java:527) [processing SIP/2.0 200 OK
current state = Proceeding Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.processResponse(SIPClientTransaction.java:530) [dialog = gov.nist.javax.sip.stack.SIPDialog@6f0565e8]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1 branchID = z9hG4bK9981c5ae61503b85f37a497fdf817bee isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:855][SIPClientTransaction.java:547][SIPClientTransaction.java:1470][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.DialogFilter.processResponse(DialogFilter.java:1023) [PROCESSING INCOMING RESPONSESIP/2.0 200 OK
Via: SIP/2.0/TCP ***************:15060;branch=z9hG4bK9981c5ae61503b85f37a497fdf817bee
From: <sip:**********2000000001@***************:15060>;tag=zlm1756388412777
To: <sip:34020000001110023168@***********:15090>;tag=00004ae1
Contact: <sip:34020000001110023168@***********:15090>
Call-ID: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: application/sdp
Content-Length: 180

]
DEBUG - gov.nist.javax.sip.DialogFilter.processResponse(DialogFilter.java:1055) [Transaction = gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2214) [sipDialog: setLastResponse:gov.nist.javax.sip.stack.SIPDialog@6f0565e8 lastResponse = SIP/2.0 200 OK
]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:2229][DialogFilter.java:1135][SIPClientTransaction.java:857][SIPClientTransaction.java:547][SIPClientTransaction.java:1470][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2230) [cseqMethod = INVITE]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2231) [dialogState = Confirmed Dialog]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2233) [method = INVITE]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2234) [statusCode = 200]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLastResponse(SIPDialog.java:2235) [transaction = gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setDialog(SIPClientTransaction.java:1521) [setDialog: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0:zlm1756388412777:00004ae1sipDialog = gov.nist.javax.sip.stack.SIPDialog@6f0565e8]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.ResponseEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:145) [Dialog = gov.nist.javax.sip.stack.SIPDialog@6f0565e8]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1139][SIPClientTransaction.java:857][SIPClientTransaction.java:547][SIPClientTransaction.java:1470][TCPMessageChannel.java:584][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@47531c39]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][TCPMessageChannel.java:589][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:201) [removing  = gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1 isReliable true]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk9981c5ae61503b85f37a497fdf817bee transaction = gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1352) [REMOVED client tx gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1 KEY = z9hg4bk9981c5ae61503b85f37a497fdf817bee]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1364][SIPClientTransaction.java:206][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@44d11978]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:258) [Client Use Count = 2]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk964]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@7b1e2115nevents 3]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.ResponseEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]source = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:281) [Calling listener for SIP/2.0 100 Trying
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][EventScanner.java:362][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@47531c39nevents 3]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.ResponseEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]source = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:281) [Calling listener for SIP/2.0 200 OK
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0:zlm1756388412777:00004ae1) : returning gov.nist.javax.sip.stack.SIPDialog@6f0565e8]
WARN - Dialog exists -- you may want to use Dialog.sendAck() Confirmed Dialog
DEBUG - gov.nist.javax.sip.stack.DefaultRouter.getNextHop(DefaultRouter.java:235) [Used request-URI for nextHop = ***********:15090/TCP]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:139) [sendBytes TCP inAddr *********** port = 15090 length = 507]
INFO - <message
from="0.0.0.0:15060" 
to="***********:15090" 
time="1756388413613"
isSender="true" 
transactionId="z9hg4bk9981c5ae61503b85f37a497fdf817bee" 
callId="c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0" 
firstLine="ACK sip:34020000001110023168@***********:15090 SIP/2.0" 
>
<![CDATA[ACK sip:34020000001110023168@***********:15090 SIP/2.0
Call-ID: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0
CSeq: 1 ACK
From: <sip:**********2000000001@***************:15060>;tag=zlm1756388412777
To: <sip:34020000001110023168@***********:15090>;tag=00004ae1
Via: SIP/2.0/TCP ***************:15060;branch=z9hG4bK9981c5ae61503b85f37a497fdf817bee
Max-Forwards: 70
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110023168:**********,**********2000000001:1
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.SipProviderImpl.sendRequest(SipProviderImpl.java:699) [done sending ACK to hop ***********:15090/TCP]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:331) [Garbage collecting unacknowledged dialog]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][EventScanner.java:362][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@44d11978nevents 3]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]source = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = gov.nist.javax.sip.stack.SIPClientTransaction@1f72ace1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk964]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk964 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk964]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@40ee953a]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@40ee953anevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]source = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab31d8]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setState(SIPDialog.java:870) [Setting dialog state for gov.nist.javax.sip.stack.SIPDialog@6f0565e8newState = 3]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:872][SIPDialog.java:1374][SIPDialog.java:286][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setState(SIPDialog.java:875) [gov.nist.javax.sip.stack.SIPDialog@6f0565e8  old dialog state is Confirmed Dialog]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setState(SIPDialog.java:877) [gov.nist.javax.sip.stack.SIPDialog@6f0565e8  New dialog state is Terminated Dialog]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.processMessage(TCPMessageChannel.java:485) [----Processing Message---]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk989]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.<init>(SIPTransaction.java:352) [use count for encapsulated channelgov.nist.javax.sip.stack.SIPServerTransaction@ffffffff 3]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][TCPMessageChannel.java:503][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK989]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( REGISTER:z9hG4bK989):gov.nist.javax.sip.stack.SIPServerTransaction@48ab321b]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for REGISTER sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@5eaa2f5d messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab321b]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@48ab321b]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][TCPMessageChannel.java:503][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.processMessage(TCPMessageChannel.java:506) [---- sipServerRequest = gov.nist.javax.sip.stack.SIPServerTransaction@48ab321b]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK989]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab321b branchID = z9hG4bK989 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][TCPMessageChannel.java:512][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK989
From: <sip:34020000001110023168@**********>;tag=0000333f
To: <sip:34020000001110023168@**********>
Contact: <sip:34020000001110023168@***********:15090>
Call-ID: 000001BE00005AED@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab321b listening point = 0.0.0.0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(000001be00005aed@***********:0000333f) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 000001be00005aed@***********:0000333f]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab321b]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [REGISTER transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][TCPMessageChannel.java:512][PipelinedMsgParser.java:353][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@29972bcb]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@29972bcbnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]source = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
INFO - <message
from="***********:15090" 
to="0.0.0.0:15060" 
time="1756388446451"
isSender="false" 
transactionId="z9hg4bk989" 
callId="000001BE00005AED@***********" 
firstLine="REGISTER sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK989
From: <sip:34020000001110023168@**********>;tag=0000333f
To: <sip:34020000001110023168@**********>
Contact: <sip:34020000001110023168@***********:15090>
Call-ID: 000001BE00005AED@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : REGISTER sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab321b sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk989 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk989]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@48ab321b]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@48ab321b]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk989 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab321b]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk989]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk989]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(000001be00005aed@***********:0000333f) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab321b branchID = z9hG4bK989 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:971][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:228][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab321b getState = Terminated Transaction]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:139) [sendBytes TCP inAddr *********** port = 15090 length = 349]
INFO - <message
from="0.0.0.0:15060" 
to="***********:15090" 
time="1756388446732"
isSender="true" 
transactionId="z9hg4bk989" 
callId="000001BE00005AED@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK989
From: <sip:34020000001110023168@**********>;tag=0000333f
To: <sip:34020000001110023168@**********>
Call-ID: 000001BE00005AED@***********
CSeq: 2 REGISTER
Date: Thu, 28 Aug 2025 13:40:46 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk989]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.run(TCPMessageChannel.java:655) [IOException  closing sock java.net.SocketException: Connection reset]
DEBUG - gov.nist.javax.sip.stack.TCPMessageProcessor.remove(TCPMessageProcessor.java:206) [Thread[TCPMessageChannelThread,5,main] removing tcp:***********:15090]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk989]
DEBUG - gov.nist.javax.sip.SipProviderImpl.getNewClientTransaction(SipProviderImpl.java:333) [could not find existing transaction for INVITE sip:34020000001110023168@***********:15090 SIP/2.0
 creating a new one ]
DEBUG - gov.nist.javax.sip.stack.DefaultRouter.getNextHop(DefaultRouter.java:235) [Used request-URI for nextHop = ***********:15090/TCP]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(a41cb0b49065d809bc8c36a2474b619f@0:0:0:0:0:0:0:0:zlm1756388448984) : returning null]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.<init>(TCPMessageChannel.java:167) [creating new TCPMessageChannel ]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[TCPMessageChannel.java:168][TCPMessageProcessor.java:225][SIPTransactionStack.java:1224][SipProviderImpl.java:379][GB28181SipServer.java:589][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.TCPMessageProcessor.createMessageChannel(TCPMessageProcessor.java:233) [key tcp:***********:15090]
DEBUG - gov.nist.javax.sip.stack.TCPMessageProcessor.createMessageChannel(TCPMessageProcessor.java:235) [Creating gov.nist.javax.sip.stack.TCPMessageChannel@47b6fa02]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.<init>(SIPTransaction.java:352) [use count for encapsulated channelgov.nist.javax.sip.stack.SIPClientTransaction@ffffffff 1]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.<init>(SIPClientTransaction.java:301) [Creating clientTransaction gov.nist.javax.sip.stack.SIPClientTransaction@ffffffff]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPClientTransaction.java:302][SIPTransactionStack.java:1253][SIPTransactionStack.java:1231][SipProviderImpl.java:379][GB28181SipServer.java:589][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK558d7497ee068b71adc100d4aaa8d1aa]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1417) [ putTransactionHash :  key = z9hg4bk558d7497ee068b71adc100d4aaa8d1aa]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK558d7497ee068b71adc100d4aaa8d1aa]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setRemoteParty(SIPDialog.java:443) [settingRemoteParty <sip:34020000001110023168@***********:15090>]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLocalSequenceNumber(SIPDialog.java:1247) [setLocalSequenceNumber: original 	0 new  = 1]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1167) [Transaction Added gov.nist.javax.sip.stack.SIPDialog@63f43b38zlm1756388448984/null]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1169) [TID = z9hg4bk558d7497ee068b71adc100d4aaa8d1aa/false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:1172][SIPDialog.java:327][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:589][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:329) [Creating a dialog : gov.nist.javax.sip.stack.SIPDialog@63f43b38]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:330) [provider port = 15060]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:332][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:589][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setDialog(SIPClientTransaction.java:1521) [setDialog: a41cb0b49065d809bc8c36a2474b619f@0:0:0:0:0:0:0:0:zlm1756388448984sipDialog = gov.nist.javax.sip.stack.SIPDialog@63f43b38]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendRequest(SIPClientTransaction.java:918) [sendRequest() INVITE sip:34020000001110023168@***********:15090 SIP/2.0
Call-ID: a41cb0b49065d809bc8c36a2474b619f@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***************:15060>;tag=zlm1756388448984
To: <sip:34020000001110023168@***********:15090>
Via: SIP/2.0/TCP ***************:15060;branch=z9hG4bK558d7497ee068b71adc100d4aaa8d1aa
Max-Forwards: 70
Contact: <sip:**********2000000001@***************:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110023168:**********,**********2000000001:1
Content-Type: application/sdp
Content-Length: 317

v=0
o=**********2000000001 0 0 IN IP4 ***********
s=Play
u=**********2000000001:0
c=IN IP4 ***********
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=**********
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:424) [Sending Message INVITE sip:34020000001110023168@***********:15090 SIP/2.0
Call-ID: a41cb0b49065d809bc8c36a2474b619f@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***************:15060>;tag=zlm1756388448984
To: <sip:34020000001110023168@***********:15090>
Via: SIP/2.0/TCP ***************:15060;branch=z9hG4bK558d7497ee068b71adc100d4aaa8d1aa
Max-Forwards: 70
Contact: <sip:**********2000000001@***************:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110023168:**********,**********2000000001:1
Content-Type: application/sdp
Content-Length: 317

v=0
o=**********2000000001 0 0 IN IP4 ***********
s=Play
u=**********2000000001:0
c=IN IP4 ***********
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=**********
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:426) [TransactionState null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK558d7497ee068b71adc100d4aaa8d1aa]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Calling Transaction gov.nist.javax.sip.stack.SIPClientTransaction@4a156155 branchID = z9hG4bK558d7497ee068b71adc100d4aaa8d1aa isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:465][SIPClientTransaction.java:983][GB28181SipServer.java:590][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPClientTransaction@4a156155 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:139) [sendBytes TCP inAddr *********** port = 15090 length = 909]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:189) [IOException occured retryCount 0]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:163) [inaddr = /***********]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:165) [port = 15090]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@4a156155 branchID = z9hG4bK558d7497ee068b71adc100d4aaa8d1aa isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:489][SIPClientTransaction.java:983][GB28181SipServer.java:590][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@4a156155 branchID = z9hG4bK558d7497ee068b71adc100d4aaa8d1aa isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:986][GB28181SipServer.java:590][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@48ab321b]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk989]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:201) [removing  = gov.nist.javax.sip.stack.SIPClientTransaction@4a156155 isReliable true]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk558d7497ee068b71adc100d4aaa8d1aa transaction = gov.nist.javax.sip.stack.SIPClientTransaction@4a156155]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1352) [REMOVED client tx gov.nist.javax.sip.stack.SIPClientTransaction@4a156155 KEY = z9hg4bk558d7497ee068b71adc100d4aaa8d1aa]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1364][SIPClientTransaction.java:206][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@7875591a]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:258) [Client Use Count = 1]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@7875591anevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]source = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = gov.nist.javax.sip.stack.SIPClientTransaction@4a156155]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = null]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.DialogTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]currentTransaction = nullthis.sipListener = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:635][SIPDialog.java:191][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@34a395b5]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@34a395b5nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.DialogTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]source = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk989]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@48ab321b]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk989 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@48ab321b]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk989]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@d0815e]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@d0815enevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]source = gov.nist.javax.sip.SipProviderImpl@5cbb4ba1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab321b]
DEBUG - gov.nist.javax.sip.SipStackImpl.stop(SipStackImpl.java:931) [stopStack -- stoppping the stack]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.close(TCPMessageChannel.java:200) [Closing message Channel gov.nist.javax.sip.stack.TCPMessageChannel@47b6fa02]
DEBUG - gov.nist.javax.sip.stack.UDPMessageProcessor.run(UDPMessageProcessor.java:251) [UDPMessageProcessor: Stopping]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:455) [Stopped event scanner!!]
