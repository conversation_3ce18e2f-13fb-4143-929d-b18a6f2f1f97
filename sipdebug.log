DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=GB28181-1756387359473, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=sipdebug.log}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="GB28181-1756387359473"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@2cd2c764 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@d17d554]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.TCPMessageProcessor@7167d81b listeningPoint = gov.nist.javax.sip.ListeningPointImpl@7cadf3ca]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@d17d554]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@2d114d27]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@7cadf3ca]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@2d114d27]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 434]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK550
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387365303"
isSender="false" 
transactionId="z9hg4bk550" 
callId="00006D6B00002195@***********" 
firstLine="REGISTER sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK550
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk550]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK550]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( REGISTER:z9hG4bK550):gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for REGISTER sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@626bd3b4 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process REGISTER sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK550]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1 branchID = z9hG4bK550 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK550
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00006d6b00002195@***********:000025ee) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00006d6b00002195@***********:000025ee]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [REGISTER transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@7ba6b123]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@7ba6b123]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@230b3dd2]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing REGISTER sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@230b3dd2nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@7ba6b123]source = gov.nist.javax.sip.SipProviderImpl@7ba6b123]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : REGISTER sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk550 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk550]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk550 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk550]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk550]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00006d6b00002195@***********:000025ee) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1 branchID = z9hG4bK550 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:481][GB28181SipServer.java:236][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:481][GB28181SipServer.java:236][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 401 Unauthorized
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK550
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 1 REGISTER
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 401 Unauthorized
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK550
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 1 REGISTER
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387365332"
isSender="true" 
transactionId="z9hg4bk550" 
callId="00006D6B00002195@***********" 
firstLine="SIP/2.0 401 Unauthorized" 
>
<![CDATA[SIP/2.0 401 Unauthorized
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK550
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 1 REGISTER
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk550]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@48ab22b1]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk550]
DEBUG - gov.nist.javax.sip.SipStackImpl.stop(SipStackImpl.java:931) [stopStack -- stoppping the stack]
DEBUG - gov.nist.javax.sip.stack.UDPMessageProcessor.run(UDPMessageProcessor.java:251) [UDPMessageProcessor: Stopping]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:455) [Stopped event scanner!!]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=GB28181-1756387394319, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=sipdebug.log}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="GB28181-1756387394319"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@516155b5 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@799fc4c9]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.TCPMessageProcessor@6c6b00f1 listeningPoint = gov.nist.javax.sip.ListeningPointImpl@48126a17]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@799fc4c9]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@7a14ab66]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@48126a17]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@7a14ab66]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 434]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK56f
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387396312"
isSender="false" 
transactionId="z9hg4bk56f" 
callId="00006D6B00002195@***********" 
firstLine="REGISTER sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK56f
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( REGISTER:z9hG4bK56f):gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for REGISTER sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@8f1ccc6 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process REGISTER sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306 branchID = z9hG4bK56f isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK56f
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00006d6b00002195@***********:000025ee) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00006d6b00002195@***********:000025ee]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [REGISTER transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@16c5deba]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing REGISTER sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@16c5debanevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : REGISTER sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk56f existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk56f existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00006d6b00002195@***********:000025ee) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306 branchID = z9hG4bK56f isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:228][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:228][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK56f
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 2 REGISTER
Date: Thu, 28 Aug 2025 13:23:17 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK56f
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 2 REGISTER
Date: Thu, 28 Aug 2025 13:23:17 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387397217"
isSender="true" 
transactionId="z9hg4bk56f" 
callId="00006D6B00002195@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK56f
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 2 REGISTER
Date: Thu, 28 Aug 2025 13:23:17 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk56f]
DEBUG - gov.nist.javax.sip.SipProviderImpl.getNewClientTransaction(SipProviderImpl.java:333) [could not find existing transaction for INVITE sip:34020000001110022400@***********:15090 SIP/2.0
 creating a new one ]
DEBUG - gov.nist.javax.sip.stack.DefaultRouter.getNextHop(DefaultRouter.java:235) [Used request-URI for nextHop = ***********:15090/TCP]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(fe0caf3189eea794022728a0e72e6e54@0:0:0:0:0:0:0:0:zlm1756387400252) : returning null]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.<init>(TCPMessageChannel.java:167) [creating new TCPMessageChannel ]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[TCPMessageChannel.java:168][TCPMessageProcessor.java:225][SIPTransactionStack.java:1224][SipProviderImpl.java:379][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.TCPMessageProcessor.createMessageChannel(TCPMessageProcessor.java:233) [key tcp:***********:15090]
DEBUG - gov.nist.javax.sip.stack.TCPMessageProcessor.createMessageChannel(TCPMessageProcessor.java:235) [Creating gov.nist.javax.sip.stack.TCPMessageChannel@499556fe]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.<init>(SIPTransaction.java:352) [use count for encapsulated channelgov.nist.javax.sip.stack.SIPClientTransaction@ffffffff 1]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.<init>(SIPClientTransaction.java:301) [Creating clientTransaction gov.nist.javax.sip.stack.SIPClientTransaction@ffffffff]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPClientTransaction.java:302][SIPTransactionStack.java:1253][SIPTransactionStack.java:1231][SipProviderImpl.java:379][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKd14fd81dfda289c0b5199b3eaadf08b0]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1417) [ putTransactionHash :  key = z9hg4bkd14fd81dfda289c0b5199b3eaadf08b0]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKd14fd81dfda289c0b5199b3eaadf08b0]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setRemoteParty(SIPDialog.java:443) [settingRemoteParty <sip:34020000001110022400@***********:15090>]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLocalSequenceNumber(SIPDialog.java:1247) [setLocalSequenceNumber: original 	0 new  = 1]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1167) [Transaction Added gov.nist.javax.sip.stack.SIPDialog@37e682a2zlm1756387400252/null]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1169) [TID = z9hg4bkd14fd81dfda289c0b5199b3eaadf08b0/false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:1172][SIPDialog.java:327][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:329) [Creating a dialog : gov.nist.javax.sip.stack.SIPDialog@37e682a2]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:330) [provider port = 15060]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:332][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setDialog(SIPClientTransaction.java:1521) [setDialog: fe0caf3189eea794022728a0e72e6e54@0:0:0:0:0:0:0:0:zlm1756387400252sipDialog = gov.nist.javax.sip.stack.SIPDialog@37e682a2]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendRequest(SIPClientTransaction.java:918) [sendRequest() INVITE sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: fe0caf3189eea794022728a0e72e6e54@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387400252
To: <sip:34020000001110022400@***********:15090>
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bKd14fd81dfda289c0b5199b3eaadf08b0
Max-Forwards: 70
Contact: <sip:**********2000000001@***********:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:0000010000,**********2000000001:1
Content-Type: application/sdp
Content-Length: 325

v=0
o=**********2000000001 0 0 IN IP4 ***************
s=Play
u=**********2000000001:0
c=IN IP4 ***************
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=0000010000
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:424) [Sending Message INVITE sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: fe0caf3189eea794022728a0e72e6e54@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387400252
To: <sip:34020000001110022400@***********:15090>
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bKd14fd81dfda289c0b5199b3eaadf08b0
Max-Forwards: 70
Contact: <sip:**********2000000001@***********:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:0000010000,**********2000000001:1
Content-Type: application/sdp
Content-Length: 325

v=0
o=**********2000000001 0 0 IN IP4 ***************
s=Play
u=**********2000000001:0
c=IN IP4 ***************
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=0000010000
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:426) [TransactionState null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKd14fd81dfda289c0b5199b3eaadf08b0]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Calling Transaction gov.nist.javax.sip.stack.SIPClientTransaction@2fa87e50 branchID = z9hG4bKd14fd81dfda289c0b5199b3eaadf08b0 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:465][SIPClientTransaction.java:983][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPClientTransaction@2fa87e50 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:139) [sendBytes TCP inAddr *********** port = 15090 length = 905]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:163) [inaddr = /***********]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:165) [port = 15090]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@2fa87e50 branchID = z9hG4bKd14fd81dfda289c0b5199b3eaadf08b0 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:489][SIPClientTransaction.java:983][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@2fa87e50 branchID = z9hG4bKd14fd81dfda289c0b5199b3eaadf08b0 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:986][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:201) [removing  = gov.nist.javax.sip.stack.SIPClientTransaction@2fa87e50 isReliable true]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bkd14fd81dfda289c0b5199b3eaadf08b0 transaction = gov.nist.javax.sip.stack.SIPClientTransaction@2fa87e50]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1352) [REMOVED client tx gov.nist.javax.sip.stack.SIPClientTransaction@2fa87e50 KEY = z9hg4bkd14fd81dfda289c0b5199b3eaadf08b0]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@776efecd]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@776efecd]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1364][SIPClientTransaction.java:206][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@4b7b1bf0]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:258) [Client Use Count = 1]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@4b7b1bf0nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@776efecd]source = gov.nist.javax.sip.SipProviderImpl@776efecd]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = gov.nist.javax.sip.stack.SIPClientTransaction@2fa87e50]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = null]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.fireTimeoutTimer(SIPServerTransaction.java:1202) [SIPServerTransaction.fireTimeoutTimer this = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306 current state = Completed Transaction method = REGISTER]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306 branchID = z9hG4bK56f isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:1227][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk56f transaction = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@45bcfcef]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@45bcfcefnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk56f transaction = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2306]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk56f]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 563]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>1</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387456308"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( MESSAGE:z9hG4bK00004823):gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for MESSAGE sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@64eaf8c0 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>1</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00004ae100006784@***********:000018be]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [MESSAGE transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@60d57085]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@60d57085nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : MESSAGE sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Date: Thu, 28 Aug 2025 13:24:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Date: Thu, 28 Aug 2025 13:24:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387456614"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Date: Thu, 28 Aug 2025 13:24:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.fireTimeoutTimer(SIPServerTransaction.java:1202) [SIPServerTransaction.fireTimeoutTimer this = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 current state = Completed Transaction method = MESSAGE]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:1227][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@481f6d4]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@481f6d4nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 563]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>2</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387516303"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( MESSAGE:z9hG4bK00004823):gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for MESSAGE sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@2e195c33 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>2</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00004ae100006784@***********:000018be]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [MESSAGE transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@20deb780]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@20deb780nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : MESSAGE sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Date: Thu, 28 Aug 2025 13:25:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Date: Thu, 28 Aug 2025 13:25:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387516602"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Date: Thu, 28 Aug 2025 13:25:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.fireTimeoutTimer(SIPServerTransaction.java:1202) [SIPServerTransaction.fireTimeoutTimer this = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 current state = Completed Transaction method = MESSAGE]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:1227][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@1736c92f]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@1736c92fnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 563]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 3 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>3</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387576309"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 3 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( MESSAGE:z9hG4bK00004823):gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for MESSAGE sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@5e594ee2 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 3 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>3</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00004ae100006784@***********:000018be]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [MESSAGE transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@509eab59]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@509eab59nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : MESSAGE sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 3 MESSAGE
Date: Thu, 28 Aug 2025 13:26:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 3 MESSAGE
Date: Thu, 28 Aug 2025 13:26:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387576851"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 3 MESSAGE
Date: Thu, 28 Aug 2025 13:26:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.fireTimeoutTimer(SIPServerTransaction.java:1202) [SIPServerTransaction.fireTimeoutTimer this = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 current state = Completed Transaction method = MESSAGE]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:1227][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@35cf14a2]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@35cf14a2nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 563]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 4 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>4</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387636315"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 4 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( MESSAGE:z9hG4bK00004823):gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for MESSAGE sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@2e11bc61 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 4 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>4</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00004ae100006784@***********:000018be]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [MESSAGE transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@20d657d3]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@20d657d3nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : MESSAGE sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 4 MESSAGE
Date: Thu, 28 Aug 2025 13:27:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 4 MESSAGE
Date: Thu, 28 Aug 2025 13:27:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387636702"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 4 MESSAGE
Date: Thu, 28 Aug 2025 13:27:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.fireTimeoutTimer(SIPServerTransaction.java:1202) [SIPServerTransaction.fireTimeoutTimer this = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 current state = Completed Transaction method = MESSAGE]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:1227][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@6f9275eb]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@6f9275ebnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 563]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 5 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>5</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387696317"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 5 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( MESSAGE:z9hG4bK00004823):gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for MESSAGE sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@75a9a00d messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 5 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>5</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00004ae100006784@***********:000018be]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [MESSAGE transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@7b6e50c8]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@7b6e50c8nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : MESSAGE sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 5 MESSAGE
Date: Thu, 28 Aug 2025 13:28:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 5 MESSAGE
Date: Thu, 28 Aug 2025 13:28:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387696908"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 5 MESSAGE
Date: Thu, 28 Aug 2025 13:28:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.fireTimeoutTimer(SIPServerTransaction.java:1202) [SIPServerTransaction.fireTimeoutTimer this = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 current state = Completed Transaction method = MESSAGE]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:1227][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1343][SIPServerTransaction.java:1228][SIPTransaction.java:631][SIPServerTransaction.java:369][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@64ae099b]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@64ae099bnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = null]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.<init>(SIPTransaction.java:260) [LingerTimer : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:272) [LingerTimer: run() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction$LingerTimer.runTask(SIPTransaction.java:283) [removinggov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bk00004823 transaction = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransactionStack.java:1325][SIPTransaction.java:284][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 563]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 6 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>6</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387756315"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 6 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( MESSAGE:z9hG4bK00004823):gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for MESSAGE sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@cda3187 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST MESSAGE sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 6 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

<?xml version="1.0" encoding="GB2312"?>
<Notify>
<CmdType>Keepalive</CmdType>
<SN>6</SN>
<DeviceID>34020000001110022400</DeviceID>
<Status>OK</Status>
</Notify>

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00004ae100006784@***********:000018be]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [MESSAGE transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@21dde6c]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing MESSAGE sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@21dde6cnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : MESSAGE sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : MESSAGE returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk00004823 existing={}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00004ae100006784@***********:000018be) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 branchID = z9hG4bK00004823 isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:254][GB28181SipServer.java:186][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 6 MESSAGE
Date: Thu, 28 Aug 2025 13:29:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 6 MESSAGE
Date: Thu, 28 Aug 2025 13:29:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387756886"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@**********>;tag=000018be
To: <sip:**********2000000001@**********>
Call-ID: 00004AE100006784@***********
CSeq: 6 MESSAGE
Date: Thu, 28 Aug 2025 13:29:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message MESSAGE sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk00004823]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:287) [UDPMessageChannel: peerAddress = ***********/15090]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:291) [Length = 431]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processIncomingDataPacket(UDPMessageChannel.java:293) [REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport;branch=z9hG4bK6df
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 3 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]
INFO - <message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387764356"
isSender="false" 
transactionId="z9hg4bk6df" 
callId="00006D6B00002195@***********" 
firstLine="REGISTER sip:**********2000000001@********** SIP/2.0" 
>
<![CDATA[REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK6df
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 3 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk6df]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.<init>(SIPServerTransaction.java:499) [Creating Server Transactionnull]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPServerTransaction.java:501][SIPTransactionStack.java:1270][SIPTransactionStack.java:1068][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK6df]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.newSIPServerRequest(SIPTransactionStack.java:1083) [newSIPServerRequest( REGISTER:z9hG4bK6df):gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.javax.sip.NistSipMessageFactoryImpl.newSIPServerRequest(NistSipMessageFactoryImpl.java:84) [Returning request interface for REGISTER sip:**********2000000001@********** SIP/2.0
 gov.nist.javax.sip.DialogFilter@3ac889e6 messageChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1141) [acquireSem [[[[gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1142][SIPTransactionStack.java:1093][UDPMessageChannel.java:444][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.acquireSem(SIPTransaction.java:1146) [acquireSem() returning : true]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:473) [About to process REGISTER sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:720) [processRequest: REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.processRequest(SIPServerTransaction.java:722) [tx state = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bK6df]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Trying Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59 branchID = z9hG4bK6df isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:732][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setPassToListener(SIPTransaction.java:1208) [setPassToListener()]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:119) [PROCESSING INCOMING REQUEST REGISTER sip:**********2000000001@********** SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK6df
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 3 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

 transactionChannel = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59 listening point = 0:0:0:0:0:0:0:0:15060]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:149) [transaction state = Trying Transaction]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00006d6b00002195@***********:000025ee) : returning null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:228) [dialogId = 00006d6b00002195@***********:000025ee]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:229) [dialog = null]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:727) [CHECK FOR OUT OF SEQ MESSAGE null transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.javax.sip.DialogFilter.processRequest(DialogFilter.java:789) [REGISTER transaction.isMapped = false]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:142) [Dialog = null]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][DialogFilter.java:1008][SIPServerTransaction.java:830][UDPMessageChannel.java:476][UDPMessageChannel.java:419][UDPMessageChannel.java:257][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@340f60cf]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.processMessage(UDPMessageChannel.java:486) [Done processing REGISTER sip:**********2000000001@********** SIP/2.0
/gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@340f60cfnevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.RequestEvent[source=gov.nist.javax.sip.SipProviderImpl@674dc29d]source = gov.nist.javax.sip.SipProviderImpl@674dc29d]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:148) [deliverEvent : REGISTER sip:**********2000000001@********** SIP/2.0
 transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59 sipEvent.serverTx = null]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk6df existing={z9hg4bk00004823=gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findPendingTransaction(SIPTransactionStack.java:907) [looking for pending tx for :z9hg4bk6df]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.putPendingTransaction(SIPTransactionStack.java:1551) [putPendingTransaction: gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:215) [Calling listener REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:219) [Calling listener gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : REGISTER returning false]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.findTransaction(SIPTransactionStack.java:797) [serverTx: looking for key z9hg4bk6df existing={z9hg4bk00004823=gov.nist.javax.sip.stack.SIPServerTransaction@8fdbbf44}]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransaction(SIPTransactionStack.java:1381) [added transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk6df]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1423) [ putTransactionHash :  key = z9hg4bk6df]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(00006d6b00002195@***********:000025ee) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Completed Transaction gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59 branchID = z9hG4bK6df isClient = false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPServerTransaction.java:1513][SIPServerTransaction.java:948][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:228][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction.sendMessage(SIPServerTransaction.java:1113) [sendMessage : tx = gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59 getState = Completed Transaction]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.<init>(UDPMessageChannel.java:196) [Creating message channel ***********/15090]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[UDPMessageChannel.java:595][SIPServerTransaction.java:466][SIPServerTransaction.java:1118][SIPServerTransaction.java:1459][GB28181SipServer.java:450][GB28181SipServer.java:228][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:728) [gov.nist.javax.sip.stack.UDPMessageChannel:sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK6df
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 3 REGISTER
Date: Thu, 28 Aug 2025 13:29:24 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:731) [*******************
]
DEBUG - gov.nist.javax.sip.stack.UDPMessageChannel.sendMessage(UDPMessageChannel.java:748) [sendMessage ***********/15090
SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK6df
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 3 REGISTER
Date: Thu, 28 Aug 2025 13:29:24 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]
INFO - <message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387764646"
isSender="true" 
transactionId="z9hg4bk6df" 
callId="00006D6B00002195@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK6df
From: <sip:34020000001110022400@**********>;tag=000025ee
To: <sip:34020000001110022400@**********>
Call-ID: 00006D6B00002195@***********
CSeq: 3 REGISTER
Date: Thu, 28 Aug 2025 13:29:24 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

DEBUG - gov.nist.javax.sip.stack.SIPServerTransaction$TransactionTimer.<init>(SIPServerTransaction.java:335) [TransactionTimer() : z9hg4bk6df]
DEBUG - gov.nist.javax.sip.SipProviderImpl.getNewClientTransaction(SipProviderImpl.java:333) [could not find existing transaction for INVITE sip:34020000001110022400@***********:15090 SIP/2.0
 creating a new one ]
DEBUG - gov.nist.javax.sip.stack.DefaultRouter.getNextHop(DefaultRouter.java:235) [Used request-URI for nextHop = ***********:15090/TCP]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.getDialog(SIPTransactionStack.java:657) [getDialog(bb3b3ad9edb4595666d7d5baf3ec7e19@0:0:0:0:0:0:0:0:zlm1756387766950) : returning null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.<init>(SIPTransaction.java:352) [use count for encapsulated channelgov.nist.javax.sip.stack.SIPClientTransaction@ffffffff 2]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.<init>(SIPClientTransaction.java:301) [Creating clientTransaction gov.nist.javax.sip.stack.SIPClientTransaction@ffffffff]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPClientTransaction.java:302][SIPTransactionStack.java:1253][SIPTransactionStack.java:1231][SipProviderImpl.java:379][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKa8a6457647f47b5d817a8bda271d3ae1]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.addTransactionHash(SIPTransactionStack.java:1417) [ putTransactionHash :  key = z9hg4bka8a6457647f47b5d817a8bda271d3ae1]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKa8a6457647f47b5d817a8bda271d3ae1]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.isDialogCreated(SIPTransactionStack.java:490) [isDialogCreated : INVITE returning true]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setRemoteParty(SIPDialog.java:443) [settingRemoteParty <sip:34020000001110022400@***********:15090>]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.setLocalSequenceNumber(SIPDialog.java:1247) [setLocalSequenceNumber: original 	0 new  = 1]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1167) [Transaction Added gov.nist.javax.sip.stack.SIPDialog@3d0ba5eezlm1756387766950/null]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.addTransaction(SIPDialog.java:1169) [TID = z9hg4bka8a6457647f47b5d817a8bda271d3ae1/false]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:1172][SIPDialog.java:327][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:329) [Creating a dialog : gov.nist.javax.sip.stack.SIPDialog@3d0ba5ee]
DEBUG - gov.nist.javax.sip.stack.SIPDialog.<init>(SIPDialog.java:330) [provider port = 15060]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPDialog.java:332][SIPTransactionStack.java:562][SipProviderImpl.java:397][GB28181SipServer.java:577][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.setDialog(SIPClientTransaction.java:1521) [setDialog: bb3b3ad9edb4595666d7d5baf3ec7e19@0:0:0:0:0:0:0:0:zlm1756387766950sipDialog = gov.nist.javax.sip.stack.SIPDialog@3d0ba5ee]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendRequest(SIPClientTransaction.java:918) [sendRequest() INVITE sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: bb3b3ad9edb4595666d7d5baf3ec7e19@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387766950
To: <sip:34020000001110022400@***********:15090>
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bKa8a6457647f47b5d817a8bda271d3ae1
Max-Forwards: 70
Contact: <sip:**********2000000001@***********:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:0000010000,**********2000000001:1
Content-Type: application/sdp
Content-Length: 325

v=0
o=**********2000000001 0 0 IN IP4 ***************
s=Play
u=**********2000000001:0
c=IN IP4 ***************
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=0000010000
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:424) [Sending Message INVITE sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: bb3b3ad9edb4595666d7d5baf3ec7e19@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:**********2000000001@***********:15060>;tag=zlm1756387766950
To: <sip:34020000001110022400@***********:15090>
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bKa8a6457647f47b5d817a8bda271d3ae1
Max-Forwards: 70
Contact: <sip:**********2000000001@***********:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:0000010000,**********2000000001:1
Content-Type: application/sdp
Content-Length: 325

v=0
o=**********2000000001 0 0 IN IP4 ***************
s=Play
u=**********2000000001:0
c=IN IP4 ***************
t=0 0
m=video 30000 RTP/AVP 96 97 98
a=recvonly
a=setup:passive
a=connection:new
a=rtpmap:96 PS/90000
a=rtpmap:97 MPEG4/90000
a=rtpmap:98 H264/90000
a=downloadspeed:0
a=streamprofile:0
y=0000010000
]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction.sendMessage(SIPClientTransaction.java:426) [TransactionState null]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setOriginalRequest(SIPTransaction.java:412) [Setting Branch id : z9hG4bKa8a6457647f47b5d817a8bda271d3ae1]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Calling Transaction gov.nist.javax.sip.stack.SIPClientTransaction@831b2f2 branchID = z9hG4bKa8a6457647f47b5d817a8bda271d3ae1 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:465][SIPClientTransaction.java:983][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.enableTimeoutTimer(SIPTransaction.java:606) [enableTimeoutTimer gov.nist.javax.sip.stack.SIPClientTransaction@831b2f2 tickCount 64 currentTickCount = -1]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:139) [sendBytes TCP inAddr *********** port = 15090 length = 905]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:163) [inaddr = /***********]
DEBUG - gov.nist.javax.sip.stack.IOHandler.sendBytes(IOHandler.java:165) [port = 15090]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@831b2f2 branchID = z9hG4bKa8a6457647f47b5d817a8bda271d3ae1 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:489][SIPClientTransaction.java:983][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.setState(SIPTransaction.java:546) [Transaction:setState Terminated Transaction gov.nist.javax.sip.stack.SIPClientTransaction@831b2f2 branchID = z9hG4bKa8a6457647f47b5d817a8bda271d3ae1 isClient = true]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:549][SIPClientTransaction.java:1308][SIPClientTransaction.java:986][GB28181SipServer.java:578][GB28181SipServer.java:521][GB28181SipServer.java:233][GB28181SipServer.java:183][EventScanner.java:223][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:226) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:246) [Done processing Message REGISTER sip:**********2000000001@********** SIP/2.0
]
DEBUG - gov.nist.javax.sip.stack.SIPTransaction.semRelease(SIPTransaction.java:1181) [semRelease ]]]]gov.nist.javax.sip.stack.SIPServerTransaction@48ab2c59]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SIPTransaction.java:1182][SIPTransaction.java:1168][SIPServerTransaction.java:1733][EventScanner.java:254][EventScanner.java:492][Thread.java:842]]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removePendingTransaction(SIPTransactionStack.java:946) [removePendingTx: z9hg4bk6df]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:201) [removing  = gov.nist.javax.sip.stack.SIPClientTransaction@831b2f2 isReliable true]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1319) [Removing Transaction = z9hg4bka8a6457647f47b5d817a8bda271d3ae1 transaction = gov.nist.javax.sip.stack.SIPClientTransaction@831b2f2]
DEBUG - gov.nist.javax.sip.stack.SIPTransactionStack.removeTransaction(SIPTransactionStack.java:1352) [REMOVED client tx gov.nist.javax.sip.stack.SIPClientTransaction@831b2f2 KEY = z9hg4bka8a6457647f47b5d817a8bda271d3ae1]
DEBUG - gov.nist.javax.sip.SipProviderImpl.handleEvent(SipProviderImpl.java:135) [handleEvent javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@776efecd]currentTransaction = <EMAIL> = <EMAIL> = gov.nist.javax.sip.SipProviderImpl@776efecd]
DEBUG - gov.nist.core.LogWriter.logStackTrace(LogWriter.java:123) [[SipProviderImpl.java:147][SIPTransactionStack.java:1364][SIPClientTransaction.java:206][SIPStackTimerTask.java:29][Timer.java:566][Timer.java:516]]
DEBUG - gov.nist.javax.sip.EventScanner.addEvent(EventScanner.java:81) [addEvent gov.nist.javax.sip.EventWrapper@4570db90]
DEBUG - gov.nist.javax.sip.stack.SIPClientTransaction$TransactionTimer.runTask(SIPClientTransaction.java:258) [Client Use Count = 2]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:487) [Processing gov.nist.javax.sip.EventWrapper@4570db90nevents 1]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:129) [sipEvent = javax.sip.TransactionTerminatedEvent[source=gov.nist.javax.sip.SipProviderImpl@776efecd]source = gov.nist.javax.sip.SipProviderImpl@776efecd]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:389) [About to deliver transactionTerminatedEvent]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:391) [tx = gov.nist.javax.sip.stack.SIPClientTransaction@831b2f2]
DEBUG - gov.nist.javax.sip.EventScanner.deliverEvent(EventScanner.java:395) [tx = null]
DEBUG - gov.nist.javax.sip.SipStackImpl.stop(SipStackImpl.java:931) [stopStack -- stoppping the stack]
DEBUG - gov.nist.javax.sip.stack.UDPMessageProcessor.run(UDPMessageProcessor.java:251) [UDPMessageProcessor: Stopping]
DEBUG - gov.nist.javax.sip.stack.TCPMessageChannel.close(TCPMessageChannel.java:200) [Closing message Channel gov.nist.javax.sip.stack.TCPMessageChannel@499556fe]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:455) [Stopped event scanner!!]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:263) [Here are the stack configuration properties 
{gov.nist.javax.sip.SERVER_LOG=sipserver.log, gov.nist.javax.sip.TRACE_LEVEL=32, javax.sip.STACK_NAME=GB28181-1756387795009, javax.sip.IP_ADDRESS=0.0.0.0, gov.nist.javax.sip.DEBUG_LOG=sipdebug.log}
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:266) [ ]]>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:267) [</debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:268) [<description
 logDescription="GB28181-1756387795009"
 name="0.0.0.0" />
]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:274) [<debug>]
DEBUG - gov.nist.javax.sip.stack.ServerLog.checkLogFile(ServerLog.java:275) [<![CDATA[ ]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = UDP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.UDPMessageProcessor@5e83298e listeningPoint = gov.nist.javax.sip.ListeningPointImpl@7167d81b]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:722) [createListeningPoint : address = 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.SipStackImpl.createListeningPoint(SipStackImpl.java:759) [Created Message Processor: 0.0.0.0 port = 15060 transport = TCP]
DEBUG - gov.nist.javax.sip.stack.MessageProcessor.setListeningPoint(MessageProcessor.java:179) [setListeningPointgov.nist.javax.sip.stack.TCPMessageProcessor@7dbd70fd listeningPoint = gov.nist.javax.sip.ListeningPointImpl@2cb53bfc]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@7167d81b]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@799fc4c9]
DEBUG - gov.nist.javax.sip.SipStackImpl.createSipProvider(SipStackImpl.java:790) [createSipProvider: gov.nist.javax.sip.ListeningPointImpl@2cb53bfc]
DEBUG - gov.nist.javax.sip.SipProviderImpl.addSipListener(SipProviderImpl.java:205) [add SipListener com.demo.service.GB28181SipServer@799fc4c9]
DEBUG - gov.nist.javax.sip.SipStackImpl.stop(SipStackImpl.java:931) [stopStack -- stoppping the stack]
DEBUG - gov.nist.javax.sip.stack.UDPMessageProcessor.run(UDPMessageProcessor.java:251) [UDPMessageProcessor: Stopping]
DEBUG - gov.nist.javax.sip.EventScanner.run(EventScanner.java:455) [Stopped event scanner!!]
