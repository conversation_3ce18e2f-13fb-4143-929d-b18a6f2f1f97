<!-- Use the  Trace Viewer in src/tools/tracesviewer to view this  trace  
Here are the stack configuration properties 
javax.sip.IP_ADDRESS= 0.0.0.0
javax.sip.STACK_NAME= GB28181-1756387359473
javax.sip.ROUTER_PATH= null
javax.sip.OUTBOUND_PROXY= null
-->
<description
 logDescription="GB28181-1756387359473"
 name="GB28181-1756387359473"
 auxInfo="null"/>
 
<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387365303"
isSender="false" 
transactionId="z9hg4bk550" 
callId="00006D6B00002195@***********" 
firstLine="REGISTER sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[REGISTER sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK550
From: <sip:34020000001110022400@5115250000>;tag=000025ee
To: <sip:34020000001110022400@5115250000>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387365332"
isSender="true" 
transactionId="z9hg4bk550" 
callId="00006D6B00002195@***********" 
firstLine="SIP/2.0 401 Unauthorized" 
>
<![CDATA[SIP/2.0 401 Unauthorized
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK550
From: <sip:34020000001110022400@5115250000>;tag=000025ee
To: <sip:34020000001110022400@5115250000>
Call-ID: 00006D6B00002195@***********
CSeq: 1 REGISTER
Content-Length: 0

]]>
</message>

<!-- Use the  Trace Viewer in src/tools/tracesviewer to view this  trace  
Here are the stack configuration properties 
javax.sip.IP_ADDRESS= 0.0.0.0
javax.sip.STACK_NAME= GB28181-1756387394319
javax.sip.ROUTER_PATH= null
javax.sip.OUTBOUND_PROXY= null
-->
<description
 logDescription="GB28181-1756387394319"
 name="GB28181-1756387394319"
 auxInfo="null"/>
 
<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387396312"
isSender="false" 
transactionId="z9hg4bk56f" 
callId="00006D6B00002195@***********" 
firstLine="REGISTER sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[REGISTER sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK56f
From: <sip:34020000001110022400@5115250000>;tag=000025ee
To: <sip:34020000001110022400@5115250000>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387397217"
isSender="true" 
transactionId="z9hg4bk56f" 
callId="00006D6B00002195@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK56f
From: <sip:34020000001110022400@5115250000>;tag=000025ee
To: <sip:34020000001110022400@5115250000>
Call-ID: 00006D6B00002195@***********
CSeq: 2 REGISTER
Date: Thu, 28 Aug 2025 13:23:17 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387456308"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[MESSAGE sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387456614"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Date: Thu, 28 Aug 2025 13:24:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387516303"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[MESSAGE sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387516602"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Date: Thu, 28 Aug 2025 13:25:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387576309"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[MESSAGE sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 3 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387576851"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 3 MESSAGE
Date: Thu, 28 Aug 2025 13:26:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387636315"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[MESSAGE sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 4 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387636702"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 4 MESSAGE
Date: Thu, 28 Aug 2025 13:27:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387696317"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[MESSAGE sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 5 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387696908"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 5 MESSAGE
Date: Thu, 28 Aug 2025 13:28:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387756315"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[MESSAGE sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 6 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387756886"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 6 MESSAGE
Date: Thu, 28 Aug 2025 13:29:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387764356"
isSender="false" 
transactionId="z9hg4bk6df" 
callId="00006D6B00002195@***********" 
firstLine="REGISTER sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[REGISTER sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK6df
From: <sip:34020000001110022400@5115250000>;tag=000025ee
To: <sip:34020000001110022400@5115250000>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 3 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387764646"
isSender="true" 
transactionId="z9hg4bk6df" 
callId="00006D6B00002195@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK6df
From: <sip:34020000001110022400@5115250000>;tag=000025ee
To: <sip:34020000001110022400@5115250000>
Call-ID: 00006D6B00002195@***********
CSeq: 3 REGISTER
Date: Thu, 28 Aug 2025 13:29:24 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<!-- Use the  Trace Viewer in src/tools/tracesviewer to view this  trace  
Here are the stack configuration properties 
javax.sip.IP_ADDRESS= 0.0.0.0
javax.sip.STACK_NAME= GB28181-1756387795009
javax.sip.ROUTER_PATH= null
javax.sip.OUTBOUND_PROXY= null
-->
<description
 logDescription="GB28181-1756387795009"
 name="GB28181-1756387795009"
 auxInfo="null"/>
 
