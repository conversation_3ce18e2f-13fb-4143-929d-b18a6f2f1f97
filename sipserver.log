<!-- Use the  Trace Viewer in src/tools/tracesviewer to view this  trace  
Here are the stack configuration properties 
javax.sip.IP_ADDRESS= 0.0.0.0
javax.sip.STACK_NAME= GB28181-1756387359473
javax.sip.ROUTER_PATH= null
javax.sip.OUTBOUND_PROXY= null
-->
<description
 logDescription="GB28181-1756387359473"
 name="GB28181-1756387359473"
 auxInfo="null"/>
 
<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387365303"
isSender="false" 
transactionId="z9hg4bk550" 
callId="00006D6B00002195@***********" 
firstLine="REGISTER sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[REGISTER sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK550
From: <sip:34020000001110022400@5115250000>;tag=000025ee
To: <sip:34020000001110022400@5115250000>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387365332"
isSender="true" 
transactionId="z9hg4bk550" 
callId="00006D6B00002195@***********" 
firstLine="SIP/2.0 401 Unauthorized" 
>
<![CDATA[SIP/2.0 401 Unauthorized
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK550
From: <sip:34020000001110022400@5115250000>;tag=000025ee
To: <sip:34020000001110022400@5115250000>
Call-ID: 00006D6B00002195@***********
CSeq: 1 REGISTER
Content-Length: 0

]]>
</message>

<!-- Use the  Trace Viewer in src/tools/tracesviewer to view this  trace  
Here are the stack configuration properties 
javax.sip.IP_ADDRESS= 0.0.0.0
javax.sip.STACK_NAME= GB28181-1756387394319
javax.sip.ROUTER_PATH= null
javax.sip.OUTBOUND_PROXY= null
-->
<description
 logDescription="GB28181-1756387394319"
 name="GB28181-1756387394319"
 auxInfo="null"/>
 
<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387396312"
isSender="false" 
transactionId="z9hg4bk56f" 
callId="00006D6B00002195@***********" 
firstLine="REGISTER sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[REGISTER sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK56f
From: <sip:34020000001110022400@5115250000>;tag=000025ee
To: <sip:34020000001110022400@5115250000>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387397217"
isSender="true" 
transactionId="z9hg4bk56f" 
callId="00006D6B00002195@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK56f
From: <sip:34020000001110022400@5115250000>;tag=000025ee
To: <sip:34020000001110022400@5115250000>
Call-ID: 00006D6B00002195@***********
CSeq: 2 REGISTER
Date: Thu, 28 Aug 2025 13:23:17 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387456308"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[MESSAGE sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387456614"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Date: Thu, 28 Aug 2025 13:24:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387516303"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[MESSAGE sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387516602"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Date: Thu, 28 Aug 2025 13:25:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387576309"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[MESSAGE sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 3 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387576851"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 3 MESSAGE
Date: Thu, 28 Aug 2025 13:26:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387636315"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[MESSAGE sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 4 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387636702"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 4 MESSAGE
Date: Thu, 28 Aug 2025 13:27:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387696317"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[MESSAGE sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 5 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387696908"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 5 MESSAGE
Date: Thu, 28 Aug 2025 13:28:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387756315"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[MESSAGE sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 6 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387756886"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 6 MESSAGE
Date: Thu, 28 Aug 2025 13:29:16 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387764356"
isSender="false" 
transactionId="z9hg4bk6df" 
callId="00006D6B00002195@***********" 
firstLine="REGISTER sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[REGISTER sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK6df
From: <sip:34020000001110022400@5115250000>;tag=000025ee
To: <sip:34020000001110022400@5115250000>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00006D6B00002195@***********
CSeq: 3 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387764646"
isSender="true" 
transactionId="z9hg4bk6df" 
callId="00006D6B00002195@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK6df
From: <sip:34020000001110022400@5115250000>;tag=000025ee
To: <sip:34020000001110022400@5115250000>
Call-ID: 00006D6B00002195@***********
CSeq: 3 REGISTER
Date: Thu, 28 Aug 2025 13:29:24 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<!-- Use the  Trace Viewer in src/tools/tracesviewer to view this  trace  
Here are the stack configuration properties 
javax.sip.IP_ADDRESS= 0.0.0.0
javax.sip.STACK_NAME= GB28181-1756387795009
javax.sip.ROUTER_PATH= null
javax.sip.OUTBOUND_PROXY= null
-->
<description
 logDescription="GB28181-1756387795009"
 name="GB28181-1756387795009"
 auxInfo="null"/>
 
<!-- Use the  Trace Viewer in src/tools/tracesviewer to view this  trace  
Here are the stack configuration properties 
javax.sip.IP_ADDRESS= 0.0.0.0
javax.sip.STACK_NAME= GB28181-1756387804847
javax.sip.ROUTER_PATH= null
javax.sip.OUTBOUND_PROXY= null
-->
<description
 logDescription="GB28181-1756387804847"
 name="GB28181-1756387804847"
 auxInfo="null"/>
 
<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387809405"
isSender="false" 
transactionId="z9hg4bk70c" 
callId="000074970000735A@***********" 
firstLine="REGISTER sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[REGISTER sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK70c
From: <sip:34020000001110022400@5115250000>;tag=00002b98
To: <sip:34020000001110022400@5115250000>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 000074970000735A@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387809734"
isSender="true" 
transactionId="z9hg4bk70c" 
callId="000074970000735A@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK70c
From: <sip:34020000001110022400@5115250000>;tag=00002b98
To: <sip:34020000001110022400@5115250000>
Call-ID: 000074970000735A@***********
CSeq: 1 REGISTER
Date: Thu, 28 Aug 2025 13:30:09 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387869409"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[MESSAGE sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387869758"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 1 MESSAGE
Date: Thu, 28 Aug 2025 13:31:09 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387929409"
isSender="false" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="MESSAGE sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[MESSAGE sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: Application/MANSCDP+xml
Content-Length: 170

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387929763"
isSender="true" 
transactionId="z9hg4bk00004823" 
callId="00004AE100006784@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK00004823
From: <sip:34020000001110022400@5115250000>;tag=000018be
To: <sip:51152500002000000001@5115250000>
Call-ID: 00004AE100006784@***********
CSeq: 2 MESSAGE
Date: Thu, 28 Aug 2025 13:32:09 GMT
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0:0:0:0:0:0:0:0:15060" 
time="1756387936722"
isSender="false" 
transactionId="z9hg4bk78b" 
callId="000074970000735A@***********" 
firstLine="REGISTER sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[REGISTER sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK78b
From: <sip:34020000001110022400@5115250000>;tag=00002b98
To: <sip:34020000001110022400@5115250000>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 000074970000735A@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

<message
from="0:0:0:0:0:0:0:0:15060" 
to="***********:15090" 
time="1756387937565"
isSender="true" 
transactionId="z9hg4bk78b" 
callId="000074970000735A@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/UDP ***********:15090;rport=15090;received=***********;branch=z9hG4bK78b
From: <sip:34020000001110022400@5115250000>;tag=00002b98
To: <sip:34020000001110022400@5115250000>
Call-ID: 000074970000735A@***********
CSeq: 2 REGISTER
Date: Thu, 28 Aug 2025 13:32:17 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0.0.0.0:15060" 
time="1756387946644"
isSender="false" 
transactionId="z9hg4bk795" 
callId="00004CB700006B6D@***********" 
firstLine="REGISTER sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[REGISTER sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK795
From: <sip:34020000001110022400@5115250000>;tag=00002d57
To: <sip:34020000001110022400@5115250000>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00004CB700006B6D@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

<message
from="0.0.0.0:15060" 
to="***********:15090" 
time="1756387947169"
isSender="true" 
transactionId="z9hg4bk795" 
callId="00004CB700006B6D@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK795
From: <sip:34020000001110022400@5115250000>;tag=00002d57
To: <sip:34020000001110022400@5115250000>
Call-ID: 00004CB700006B6D@***********
CSeq: 1 REGISTER
Date: Thu, 28 Aug 2025 13:32:27 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="0.0.0.0:15060" 
to="***********:15090" 
time="1756387949555"
isSender="true" 
transactionId="z9hg4bk778238729c1ea591c739d271d33b02ef" 
callId="7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0" 
firstLine="INVITE sip:34020000001110022400@***********:15090 SIP/2.0" 
>
<![CDATA[INVITE sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:51152500002000000001@***********:15060>;tag=zlm1756387949554
To: <sip:34020000001110022400@***********:15090>
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK778238729c1ea591c739d271d33b02ef
Max-Forwards: 70
Contact: <sip:51152500002000000001@***********:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:0000010000,51152500002000000001:1
Content-Type: application/sdp
Content-Length: 317

]]>
</message>

<message
from="***********:15090" 
to="0.0.0.0:15060" 
time="1756387949556"
isSender="false" 
transactionId="z9hg4bk778238729c1ea591c739d271d33b02ef" 
callId="7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0" 
firstLine="SIP/2.0 100 Trying" 
>
<![CDATA[SIP/2.0 100 Trying
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK778238729c1ea591c739d271d33b02ef
From: <sip:51152500002000000001@***********:15060>;tag=zlm1756387949554
To: <sip:34020000001110022400@***********:15090>;tag=00004ae1
Call-ID: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0.0.0.0:15060" 
time="1756387949556"
isSender="false" 
transactionId="z9hg4bk778238729c1ea591c739d271d33b02ef" 
callId="7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK778238729c1ea591c739d271d33b02ef
From: <sip:51152500002000000001@***********:15060>;tag=zlm1756387949554
To: <sip:34020000001110022400@***********:15090>;tag=00004ae1
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: application/sdp
Content-Length: 180

]]>
</message>

<message
from="0.0.0.0:15060" 
to="***********:15090" 
time="1756387950072"
isSender="true" 
transactionId="z9hg4bk778238729c1ea591c739d271d33b02ef" 
callId="7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0" 
firstLine="ACK sip:34020000001110022400@***********:15090 SIP/2.0" 
>
<![CDATA[ACK sip:34020000001110022400@***********:15090 SIP/2.0
Call-ID: 7611270eb9639fbc0939784b3d21b926@0:0:0:0:0:0:0:0
CSeq: 1 ACK
From: <sip:51152500002000000001@***********:15060>;tag=zlm1756387949554
To: <sip:34020000001110022400@***********:15090>;tag=00004ae1
Via: SIP/2.0/TCP ***********:15060;branch=z9hG4bK778238729c1ea591c739d271d33b02ef
Max-Forwards: 70
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110022400:0000010000,51152500002000000001:1
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0.0.0.0:15060" 
time="1756387960494"
isSender="false" 
transactionId="z9hg4bk7a3" 
callId="00004CB700006B6D@***********" 
firstLine="REGISTER sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[REGISTER sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK7a3
From: <sip:34020000001110022400@5115250000>;tag=00002d57
To: <sip:34020000001110022400@5115250000>
Contact: <sip:34020000001110022400@***********:15090>
Call-ID: 00004CB700006B6D@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

<message
from="0.0.0.0:15060" 
to="***********:15090" 
time="1756387961095"
isSender="true" 
transactionId="z9hg4bk7a3" 
callId="00004CB700006B6D@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK7a3
From: <sip:34020000001110022400@5115250000>;tag=00002d57
To: <sip:34020000001110022400@5115250000>
Call-ID: 00004CB700006B6D@***********
CSeq: 2 REGISTER
Date: Thu, 28 Aug 2025 13:32:41 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<!-- Use the  Trace Viewer in src/tools/tracesviewer to view this  trace  
Here are the stack configuration properties 
javax.sip.IP_ADDRESS= 0.0.0.0
javax.sip.STACK_NAME= GB28181-1756388258158
javax.sip.ROUTER_PATH= null
javax.sip.OUTBOUND_PROXY= null
-->
<description
 logDescription="GB28181-1756388258158"
 name="GB28181-1756388258158"
 auxInfo="null"/>
 
<!-- Use the  Trace Viewer in src/tools/tracesviewer to view this  trace  
Here are the stack configuration properties 
javax.sip.IP_ADDRESS= 0.0.0.0
javax.sip.STACK_NAME= GB28181-1756388332247
javax.sip.ROUTER_PATH= null
javax.sip.OUTBOUND_PROXY= null
-->
<description
 logDescription="GB28181-1756388332247"
 name="GB28181-1756388332247"
 auxInfo="null"/>
 
<!-- Use the  Trace Viewer in src/tools/tracesviewer to view this  trace  
Here are the stack configuration properties 
javax.sip.IP_ADDRESS= 0.0.0.0
javax.sip.STACK_NAME= GB28181-1756388382838
javax.sip.ROUTER_PATH= null
javax.sip.OUTBOUND_PROXY= null
-->
<description
 logDescription="GB28181-1756388382838"
 name="GB28181-1756388382838"
 auxInfo="null"/>
 
<message
from="***********:15090" 
to="0.0.0.0:15060" 
time="1756388409398"
isSender="false" 
transactionId="z9hg4bk964" 
callId="000001BE00005AED@***********" 
firstLine="REGISTER sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[REGISTER sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK964
From: <sip:34020000001110023168@5115250000>;tag=0000333f
To: <sip:34020000001110023168@5115250000>
Contact: <sip:34020000001110023168@***********:15090>
Call-ID: 000001BE00005AED@***********
CSeq: 1 REGISTER
Max-Forwards: 70
Expires: 3600
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

<message
from="0.0.0.0:15060" 
to="***********:15090" 
time="1756388410259"
isSender="true" 
transactionId="z9hg4bk964" 
callId="000001BE00005AED@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK964
From: <sip:34020000001110023168@5115250000>;tag=0000333f
To: <sip:34020000001110023168@5115250000>
Call-ID: 000001BE00005AED@***********
CSeq: 1 REGISTER
Date: Thu, 28 Aug 2025 13:40:10 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

<message
from="0.0.0.0:15060" 
to="***********:15090" 
time="1756388412783"
isSender="true" 
transactionId="z9hg4bk9981c5ae61503b85f37a497fdf817bee" 
callId="c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0" 
firstLine="INVITE sip:34020000001110023168@***********:15090 SIP/2.0" 
>
<![CDATA[INVITE sip:34020000001110023168@***********:15090 SIP/2.0
Call-ID: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
From: <sip:51152500002000000001@***************:15060>;tag=zlm1756388412777
To: <sip:34020000001110023168@***********:15090>
Via: SIP/2.0/TCP ***************:15060;branch=z9hG4bK9981c5ae61503b85f37a497fdf817bee
Max-Forwards: 70
Contact: <sip:51152500002000000001@***************:15060>
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110023168:0000010000,51152500002000000001:1
Content-Type: application/sdp
Content-Length: 317

]]>
</message>

<message
from="***********:15090" 
to="0.0.0.0:15060" 
time="1756388412784"
isSender="false" 
transactionId="z9hg4bk9981c5ae61503b85f37a497fdf817bee" 
callId="c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0" 
firstLine="SIP/2.0 100 Trying" 
>
<![CDATA[SIP/2.0 100 Trying
Via: SIP/2.0/TCP ***************:15060;branch=z9hG4bK9981c5ae61503b85f37a497fdf817bee
From: <sip:51152500002000000001@***************:15060>;tag=zlm1756388412777
To: <sip:34020000001110023168@***********:15090>;tag=00004ae1
Call-ID: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0.0.0.0:15060" 
time="1756388412785"
isSender="false" 
transactionId="z9hg4bk9981c5ae61503b85f37a497fdf817bee" 
callId="c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/TCP ***************:15060;branch=z9hG4bK9981c5ae61503b85f37a497fdf817bee
From: <sip:51152500002000000001@***************:15060>;tag=zlm1756388412777
To: <sip:34020000001110023168@***********:15090>;tag=00004ae1
Contact: <sip:34020000001110023168@***********:15090>
Call-ID: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0
CSeq: 1 INVITE
Max-Forwards: 70
User-Agent: Easy GB28181 Device V7.0
Content-Type: application/sdp
Content-Length: 180

]]>
</message>

<message
from="0.0.0.0:15060" 
to="***********:15090" 
time="1756388413613"
isSender="true" 
transactionId="z9hg4bk9981c5ae61503b85f37a497fdf817bee" 
callId="c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0" 
firstLine="ACK sip:34020000001110023168@***********:15090 SIP/2.0" 
>
<![CDATA[ACK sip:34020000001110023168@***********:15090 SIP/2.0
Call-ID: c124d285a91591f2b50c3e50414a95ca@0:0:0:0:0:0:0:0
CSeq: 1 ACK
From: <sip:51152500002000000001@***************:15060>;tag=zlm1756388412777
To: <sip:34020000001110023168@***********:15090>;tag=00004ae1
Via: SIP/2.0/TCP ***************:15060;branch=z9hG4bK9981c5ae61503b85f37a497fdf817bee
Max-Forwards: 70
User-Agent: GB28181 Server V2.0
X-GB-Ver: 2.0
Subject: 34020000001110023168:0000010000,51152500002000000001:1
Content-Length: 0

]]>
</message>

<message
from="***********:15090" 
to="0.0.0.0:15060" 
time="1756388446451"
isSender="false" 
transactionId="z9hg4bk989" 
callId="000001BE00005AED@***********" 
firstLine="REGISTER sip:51152500002000000001@5115250000 SIP/2.0" 
>
<![CDATA[REGISTER sip:51152500002000000001@5115250000 SIP/2.0
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK989
From: <sip:34020000001110023168@5115250000>;tag=0000333f
To: <sip:34020000001110023168@5115250000>
Contact: <sip:34020000001110023168@***********:15090>
Call-ID: 000001BE00005AED@***********
CSeq: 2 REGISTER
Max-Forwards: 70
Expires: 0
X-GB-Ver: 2.0
User-Agent: Easy GB28181 Device V7.0
Content-Length: 0

]]>
</message>

<message
from="0.0.0.0:15060" 
to="***********:15090" 
time="1756388446732"
isSender="true" 
transactionId="z9hg4bk989" 
callId="000001BE00005AED@***********" 
firstLine="SIP/2.0 200 OK" 
>
<![CDATA[SIP/2.0 200 OK
Via: SIP/2.0/TCP ***********:15090;rport=15090;received=***********;branch=z9hG4bK989
From: <sip:34020000001110023168@5115250000>;tag=0000333f
To: <sip:34020000001110023168@5115250000>
Call-ID: 000001BE00005AED@***********
CSeq: 2 REGISTER
Date: Thu, 28 Aug 2025 13:40:46 GMT
Expires: 3600
X-GB-Ver: 2.0
Content-Length: 0

]]>
</message>

